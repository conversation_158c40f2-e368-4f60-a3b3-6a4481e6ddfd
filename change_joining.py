
#!/usr/bin/env python3
"""
change_joining.py

Fetch users from MySQL customers table and update MongoDB client collection
with date of joining as createdAt field.
"""

import os
import dotenv
import sys
import pymongo
from pymongo import UpdateOne
import pymysql
from datetime import datetime, date
from typing import Dict, List, Optional
import logging
from bson import ObjectId

dotenv.load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
MYSQL_CONFIG = {
    'host': os.getenv('MYSQL_HOST', 'localhost'),
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': os.getenv('MYSQL_USER', 'root'),
    'password': os.getenv('MYSQL_PASSWORD', ''),
    'database': os.getenv('MYSQL_DATABASE', 'climb_central'),
    'charset': 'utf8mb4',
    'autocommit': True
}

MONGO_CONFIG = {
    'uri': os.getenv('MONGODB_URI', 'mongodb://localhost:27017'),
    'database': 'hopwellness-production',
}

BATCH_SIZE = 1000

def connect_mysql():
    """Connect to MySQL database"""
    try:
        connection = pymysql.connect(
            host=MYSQL_CONFIG['host'],
            port=MYSQL_CONFIG['port'],
            user=MYSQL_CONFIG['user'],
            password=MYSQL_CONFIG['password'],
            database=MYSQL_CONFIG['database'],
            cursorclass=pymysql.cursors.DictCursor
        )
        logger.info("Connected to MySQL successfully")
        return connection
    except Exception as e:
        logger.error(f"Failed to connect to MySQL: {e}")
        raise

def connect_mongo():
    """Connect to MongoDB database"""
    try:
        client = pymongo.MongoClient(MONGO_CONFIG['uri'])
        db = client[MONGO_CONFIG['database']]
        logger.info("Connected to MongoDB successfully")
        return client, db
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise

def fetch_customers_with_join_dates(mysql_conn) -> List[Dict]:
    """Fetch all customers with join dates from MySQL"""
    try:
        with mysql_conn.cursor() as cursor:
            query = """
                SELECT CUSTOMER_ID as customer_id, 
                       FIRST_CONTACT_DATE as date_of_joining
                FROM customers 
                WHERE FIRST_CONTACT_DATE IS NOT NULL
            """
            cursor.execute(query)
            customers = cursor.fetchall()
            logger.info(f"Found {len(customers)} customers with join dates")
            return customers
    except Exception as e:
        logger.error(f"Error fetching customers: {e}")
        raise

def find_user_in_mongo(db, customer: Dict) -> Optional[Dict]:
    """Find corresponding user in MongoDB"""
    users_collection = db['users']
    
    # Build query conditions
    query_conditions = []
    
    if customer.get('customer_id'):
        query_conditions.append({'id': str(customer['customer_id'])})
    
    if not query_conditions:
        return None
    
    query = {'$or': query_conditions}
    user = users_collection.find_one(query)
    return user

def update_client_created_at(db, user_id: str, join_date: datetime) -> bool:
    """Update client's createdAt field with join date"""
    clients_collection = db['clients']
    
    try:
        result = clients_collection.update_one(
            {'userId': pymongo.ObjectId(user_id)},
            {'$set': {'createdAt': join_date}}
        )
        return result.modified_count > 0
    except Exception as e:
        logger.error(f"Error updating client for user {user_id}: {e}")
        return False

def sync_customer_join_dates():
    """Main function to sync customer join dates"""
    mysql_conn = None
    mongo_client = None
    
    try:
        # Connect to databases
        mysql_conn = connect_mysql()
        mongo_client, mongo_db = connect_mongo()
        
        # Fetch customers from MySQL
        customers = fetch_customers_with_join_dates(mysql_conn)
        
        if not customers:
            logger.info("No customers with join dates found")
            return
        
        # Process customers in batches
        updated_count = 0
        not_found_count = 0
        
        for i in range(0, len(customers), BATCH_SIZE):
            batch = customers[i:i + BATCH_SIZE]
            logger.info(f"Processing batch {i//BATCH_SIZE + 1}/{(len(customers) + BATCH_SIZE - 1)//BATCH_SIZE}")
            
            customers_ids = [int(customer['customer_id']) for customer in batch]
            organization_id = ObjectId(os.getenv('ORGANIZATION_ID'))
            users = mongo_db['users'].find({ 'organizationId': organization_id, 'id': {'$in': customers_ids}}, {'_id': 1, 'id': 1})
            
            users_map = {user['id']: user['_id'] for user in users}
            
            bulk_ops = []
            for customer in batch:
                user_id = users_map.get(customer['customer_id'])
                if user_id:
                    join_date = customer['date_of_joining']
                    
                    # Convert date to datetime if needed
                    if isinstance(join_date, date) and not isinstance(join_date, datetime):
                        join_date = datetime.combine(join_date, datetime.min.time())
                    elif isinstance(join_date, str):
                        join_date = datetime.strptime(join_date, '%Y-%m-%d')
                    
                    bulk_ops.append(UpdateOne(
                        {'userId': user_id},
                        {'$set': {'createdAt': join_date}}
                    ))
                    updated_count += 1
                    logger.debug(f"Updated client for user {user_id} with join date {join_date}")
                else:
                    not_found_count += 1
                    logger.debug(f"User not found for customer {customer.get('customer_id', 'unknown')}")
            
            if bulk_ops:
                res = mongo_db['clients'].bulk_write(bulk_ops)
                logger.info(f"Batch executed: {res.modified_count} clients updated")
        
        logger.info(f"Sync completed: {updated_count} clients updated, {not_found_count} users not found")
        
    except Exception as e:
        logger.error(f"Error during sync: {e}")
        raise
    finally:
        # Close connections
        if mysql_conn:
            mysql_conn.close()
            logger.info("MySQL connection closed")
        
        if mongo_client:
            mongo_client.close()
            logger.info("MongoDB connection closed")

def main():
    """Main entry point"""
    try:
        logger.info("Starting customer join dates sync...")
        sync_customer_join_dates()
        logger.info("Sync completed successfully")
    except Exception as e:
        logger.error(f"Sync failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

