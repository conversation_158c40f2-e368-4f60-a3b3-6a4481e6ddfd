import { Command } from 'commander';

export function createAppointmentTypeCommand(): Command {
  const appointmentTypeCommand = new Command('appointment-type');

  appointmentTypeCommand
    .description('Migrate appointment type data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        // await migrateAppointmentTypes(options.database);
      } catch (error) {
        console.error('Error executing appointment type migration command:', error);
        process.exit(1);
      }
    });

  return appointmentTypeCommand;
}
