import { Command } from 'commander';
import { migrateCustomPackage } from '../modules/pricing/custom-package.migration';

export function createCustomPackageCommand(): Command {
  const pricingCommand = new Command('custom-package');

  pricingCommand
    .description('Migrate custom package data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateCustomPackage(options.database);
      } catch (error) {
        console.error('Error executing custom package migration command:', error);
        process.exit(1);
      }
    });

  return pricingCommand;
}
