import { Command } from 'commander';
import { migrateDocumentLocker } from '../modules/document-locker/document-locker.migration';

export function createDocumentLockerCommand(): Command {
  const documentLockerCommand = new Command('document-locker');

  documentLockerCommand
    .description('Migrate document locker data from CSV to MongoDB with optional S3 upload and configurable batch processing')
    .option('-d, --database <name>', 'Database name', 'hop-migration')
    .option('--no-s3', 'Disable S3 upload and use local file paths')
    .option('--s3', 'Enable S3 upload (default)')
    .option('--processing-batch-size <size>', 'Number of documents to process in parallel', '20')
    .option('--db-batch-size <size>', 'Number of documents to save in one transaction', '50')
    .option('--max-retries <count>', 'Maximum retries for failed batches', '3')
    .option('--transaction-timeout <ms>', 'Transaction timeout in milliseconds', '30000')
    .action(async (options) => {
      try {
        const uploadToS3 = options.s3 !== false; // Default to true unless --no-s3 is specified
        console.log(`S3 upload: ${uploadToS3 ? 'enabled' : 'disabled'}`);

        // Parse batch configuration options
        const batchConfig = {
          processingBatchSize: parseInt(options.processingBatchSize, 10),
          dbBatchSize: parseInt(options.dbBatchSize, 10),
          maxRetries: parseInt(options.maxRetries, 10),
          transactionTimeoutMs: parseInt(options.transactionTimeout, 10)
        };

        console.log('Batch configuration:', batchConfig);

        await migrateDocumentLocker(options.database, batchConfig);
      } catch (error) {
        console.error('Error executing document locker migration command:', error);
        process.exit(1);
      }
    });

  return documentLockerCommand;
}
