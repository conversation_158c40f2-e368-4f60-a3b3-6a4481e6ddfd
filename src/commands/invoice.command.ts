import { Command } from 'commander';
import { migrateInvoices } from '../modules/invoice/invoice.migration';

export function createInvoiceCommand(): Command {
  const invoiceCommand = new Command('invoice');
  
  invoiceCommand
    .description('Migrate invoice and purchase data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migrateInvoices(options.database);
      } catch (error) {
        console.error('Error executing invoice migration command:', error);
        process.exit(1);
      }
    });
  
  return invoiceCommand;
}
