import { Command } from 'commander';
import { connectToMongo, closeConnection } from '../common/database/db.module';
import { MigrationTrackerService } from '../modules/migration/migration-tracker.service';
import { MigrationType } from '../modules/migration/migration-tracker.model';
import LoggerConfig from '../common/logger/log.module';

const logger = LoggerConfig('migration-history.command');

export function createMigrationHistoryCommand(): Command {
  const migrationHistoryCommand = new Command('migration-history');

  migrationHistoryCommand
    .description('View migration history for an organization')
    .option('-o, --organization <id>', 'Organization ID')
    .option('-t, --type <type>', 'Migration type', 'all')
    .option('-l, --limit <n>', 'Number of records to show', '10')
    .action(async (options) => {
      try {
        // Validate organization ID
        if (!options.organization) {
          console.error('Organization ID is required');
          process.exit(1);
        }

        // Validate migration type
        const migrationType = options.type.toUpperCase();
        if (!Object.values(MigrationType).includes(migrationType) && migrationType !== 'ALL') {
          console.error(`Invalid migration type: ${options.type}`);
          console.error(`Valid types are: ${Object.values(MigrationType).join(', ')}`);
          process.exit(1);
        }

        // Connect to database
        await connectToMongo();

        // Get migration history
        let history;
        if (migrationType === 'ALL') {
          history = await MigrationTrackerService.getMigrationHistory(
            options.organization,
            parseInt(options.limit)
          );
        } else {
          const latest = await MigrationTrackerService.getLatestMigration(
            options.organization,
            migrationType as MigrationType
          );
          history = latest ? [latest] : [];
        }

        // Display migration history
        if (history.length === 0) {
          console.log('No migration history found');
        } else {
          console.log('Migration History:');
          console.log('=================');

          history.forEach((migration, index) => {
            console.log(`\n[${index + 1}] Migration: ${migration.migrationType}`);
            console.log(`Status: ${migration.status}`);
            console.log(`Started: ${migration.startTime}`);
            console.log(`Ended: ${migration.endTime || 'N/A'}`);

            if (migration.details.length > 0) {
              console.log('\nDetails:');
              migration.details.forEach(detail => {
                console.log(`  - Module: ${detail.module}`);
                console.log(`    Processed: ${detail.recordsProcessed}`);
                console.log(`    Succeeded: ${detail.recordsSucceeded}`);
                console.log(`    Failed: ${detail.recordsFailed}`);

                if (detail.errors && detail.errors.length > 0) {
                  console.log('    Errors:');
                  detail.errors.forEach(error => {
                    console.log(`      * ${error}`);
                  });
                }
              });
            }
          });
        }
      } catch (error) {
        logger.error('Error executing migration history command:', error);
        process.exit(1);
      } finally {
        // Close the connection
        await closeConnection();
      }
    });

  return migrationHistoryCommand;
}
