import { Command } from 'commander';
import { migratePricing } from '../modules/pricing/pricing.migration';

export function createPricingCommand(): Command {
  const pricingCommand = new Command('pricing');
  
  pricingCommand
    .description('Migrate pricing data from CSV to MongoDB')
    .option('-d, --database <n>', 'Database name', 'hop-migration')
    .action(async (options) => {
      try {
        await migratePricing(options.database);
      } catch (error) {
        console.error('Error executing pricing migration command:', error);
        process.exit(1);
      }
    });
  
  return pricingCommand;
}
