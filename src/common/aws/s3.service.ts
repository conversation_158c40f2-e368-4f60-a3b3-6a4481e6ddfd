// s3Service.ts
import {
    S3Client,
    PutObjectCommand,
    GetO<PERSON>Command,
    ListObjectsV2Command,
    DeleteObjectCommand,
    CreateBucketCommand,
    DeleteBucketCommand,
    HeadObjectCommand,
    _Object,
    ObjectCannedACL,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { Upload } from "@aws-sdk/lib-storage";
import { Readable } from "stream";
import { lookup as mimeLookup } from "mime-types";

/**
 * Options for S3Service
 */
export interface S3ServiceOptions {
    region?: string;
    accessKeyId?: string;
    secretAccessKey?: string;
    endpoint?: string; // e.g., custom S3-compatible endpoint
    forcePathStyle?: boolean; // for S3-compatible storages
    defaultBucket?: string;
}

/**
 * S3Service: dynamic, full-featured wrapper around AWS S3 (SDK v3)
 */
export class S3Service {
    private client: S3Client;
    private defaultBucket?: string;

    constructor(opts: S3ServiceOptions = {}) {
        const {
            region = process.env.AWS_REGION || "us-east-1",
            accessKeyId = process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY,
            endpoint,
            forcePathStyle,
            defaultBucket,
        } = opts;

        this.defaultBucket = defaultBucket ?? process.env.S3_DEFAULT_BUCKET;

        this.client = new S3Client({
            region,
            endpoint,
            forcePathStyle,
            credentials:
                accessKeyId && secretAccessKey
                    ? { accessKeyId, secretAccessKey }
                    : undefined,
        });
    }

    /** Utility to resolve bucket (use provided or default) */
    private resolveBucket(bucket?: string) {
        const b = bucket ?? this.defaultBucket;
        if (!b) throw new Error("Bucket name not provided and no default configured.");
        return b;
    }

    /** Upload a buffer (small files) */
    async uploadBuffer(
        key: string,
        buffer: Buffer,
        bucket?: string,
        contentType?: string,
        acl?: ObjectCannedACL
    ) {
        const Bucket = this.resolveBucket(bucket);
        const ContentType = contentType || mimeLookup(key) || "application/octet-stream";

        await this.client.send(
            new PutObjectCommand({
                Bucket,
                Key: key,
                Body: buffer,
                ContentType,
                ACL: acl,
            })
        );

        return { bucket: Bucket, key };
    }

    /** Upload a stream or large file using multipart (lib-storage Upload) */
    async uploadStream(
        key: string,
        stream: Readable,
        bucket?: string,
        contentType?: string,
        partSize = 5 * 1024 * 1024 // 5 MB part size default
    ) {
        const Bucket = this.resolveBucket(bucket);
        const ContentType = contentType || mimeLookup(key) || "application/octet-stream";

        const upload = new Upload({
            client: this.client,
            params: {
                Bucket,
                Key: key,
                Body: stream,
                ContentType,
                ACL: "public-read",
            },
            queueSize: 4, // concurrent parts
            partSize,
        });

        await upload.done();
        return { bucket: Bucket, key };
    }

    /** Get object as stream */
    async getObjectStream(key: string, bucket?: string) {
        const Bucket = this.resolveBucket(bucket);
        const res = await this.client.send(new GetObjectCommand({ Bucket, Key: key }));
        // res.Body is a stream in Node
        return res.Body as Readable | undefined;
    }

    /** Download object fully to Buffer (not recommended for huge files) */
    async getObjectBuffer(key: string, bucket?: string) {
        const stream = await this.getObjectStream(key, bucket);
        if (!stream) return null;
        const chunks: Buffer[] = [];
        for await (const chunk of stream) {
            chunks.push(Buffer.from(chunk));
        }
        return Buffer.concat(chunks);
    }

    /** Get metadata (head) */
    async headObject(key: string, bucket?: string) {
        const Bucket = this.resolveBucket(bucket);
        try {
            const res = await this.client.send(new HeadObjectCommand({ Bucket, Key: key }));
            return res;
        } catch (err: any) {
            if (err.name === "NotFound" || err.$metadata?.httpStatusCode === 404) {
                return null;
            }
            throw err;
        }
    }

    /** Generate presigned GET URL (read) */
    async getPresignedGetUrl(key: string, expiresInSeconds = 900, bucket?: string) {
        const Bucket = this.resolveBucket(bucket);
        const command = new GetObjectCommand({ Bucket, Key: key });
        return getSignedUrl(this.client, command, { expiresIn: expiresInSeconds });
    }

    /** Generate presigned PUT URL (upload directly from client) */
    async getPresignedPutUrl(
        key: string,
        expiresInSeconds = 900,
        bucket?: string,
        contentType?: string
    ) {
        const Bucket = this.resolveBucket(bucket);
        const command = new PutObjectCommand({
            Bucket,
            Key: key,
            ContentType: contentType || mimeLookup(key) || "application/octet-stream",
        });
        return getSignedUrl(this.client, command, { expiresIn: expiresInSeconds });
    }

    /** List objects under prefix (with continuation handling) */
    async listObjects(prefix = "", bucket?: string, maxKeys = 1000) {
        const Bucket = this.resolveBucket(bucket);
        const objects: _Object[] = [];
        let continuationToken: string | undefined;
        do {
            const res = await this.client.send(
                new ListObjectsV2Command({
                    Bucket,
                    Prefix: prefix,
                    ContinuationToken: continuationToken,
                    MaxKeys: maxKeys,
                })
            );
            if (res.Contents) objects.push(...res.Contents);
            continuationToken = res.IsTruncated ? res.NextContinuationToken : undefined;
        } while (continuationToken);

        return objects;
    }

    /** Delete single object */
    async deleteObject(key: string, bucket?: string) {
        const Bucket = this.resolveBucket(bucket);
        await this.client.send(new DeleteObjectCommand({ Bucket, Key: key }));
        return { bucket: Bucket, key };
    }

    /** Create bucket (note: for non-us-east-1 regions you might need LocationConstraint) */
    async createBucket(bucketName: string) {
        await this.client.send(new CreateBucketCommand({ Bucket: bucketName }));
        return { created: true, bucket: bucketName };
    }

    /** Delete bucket (must be empty) */
    async deleteBucket(bucketName: string) {
        await this.client.send(new DeleteBucketCommand({ Bucket: bucketName }));
        return { deleted: true, bucket: bucketName };
    }
}

/** ---------- Usage Examples ----------
import fs from "fs";
import { S3Service } from "./s3Service";

(async () => {
  const s3 = new S3Service({ defaultBucket: "my-bucket", region: "ap-south-1" });

  // Upload buffer
  const buf = Buffer.from("hello world");
  await s3.uploadBuffer("folder/hello.txt", buf);

  // Upload from file stream (multipart friendly)
  const rs = fs.createReadStream("./bigfile.zip");
  await s3.uploadStream("backups/bigfile.zip", rs);

  // Get presigned PUT URL (let browsers upload directly)
  const putUrl = await s3.getPresignedPutUrl("uploads/image.png", 60 * 10);
  console.log("PUT URL:", putUrl);

  // Get presigned GET URL
  const getUrl = await s3.getPresignedGetUrl("folder/hello.txt", 60 * 5);
  console.log("GET URL:", getUrl);

  // Download file to buffer
  const data = await s3.getObjectBuffer("folder/hello.txt");
  console.log(data?.toString());

  // List objects under a prefix
  const list = await s3.listObjects("folder/");
  console.log("Objects:", list.map(o => o.Key));

  // Delete object
  await s3.deleteObject("folder/hello.txt");
})();
------------------------------------- */

export default S3Service;
