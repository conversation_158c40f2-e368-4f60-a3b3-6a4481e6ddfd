import mongoose, { QueryOptions } from 'mongoose';
import * as dotenv from 'dotenv';

dotenv.config();

const mongoUrl = process.env.MONGODB_URI;

if (!mongoUrl) {
  console.error("Error: MONGODB_URI is not defined in .env file");
  process.exit(1);
}

/**
 * Connect to MongoDB using Mongoose
 */
export async function connectToMongo(): Promise<typeof mongoose> {
  try {
    if (mongoose.connection.readyState === 1) {
      return mongoose; // Already connected
    }

    console.log('Connecting to MongoDB at:', mongoUrl.split('@')[1]); // Log only the server part, not credentials
    // Enable query debugging
    mongoose.set('debug', !!process.env.MONGO_DEBUG);


    await mongoose.connect(mongoUrl, {
      serverSelectionTimeoutMS: 60000, // Increase timeout to 60 seconds
      socketTimeoutMS: 90000, // Socket timeout
      connectTimeoutMS: 60000, // Connection timeout
      heartbeatFrequencyMS: 10000, // Check server status every 10 seconds
      maxPoolSize: 10, // Maintain up to 10 socket connections
      bufferCommands: true, // Buffer commands when connection is lost
      autoIndex: false, // Disable automatic index creation
    });

    console.log('Connected successfully to MongoDB using Mongoose');
    return mongoose;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

/**
 * Get database instance
 * Note: With Mongoose, you don't need to explicitly get the database
 * This is kept for backward compatibility
 */
export async function getDb(dbName: string = 'hop-migration'): Promise<mongoose.Connection> {
  await connectToMongo();
  return mongoose.connection;
}

/**
 * Close MongoDB connection
 */
export async function closeConnection(): Promise<void> {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

/**
 * This function is kept for backward compatibility
 * With Mongoose, you should use the model directly instead of getting a collection
 */
export async function getCollection(name: string): Promise<mongoose.Collection> {
  await connectToMongo();
  return mongoose.connection.collection(name);
}

/**
 * Get a MongoDB collection directly without using Mongoose models
 * This allows direct access to the MongoDB native driver collection
 */
export async function getMongoCollection(collectionName: string): Promise<any> {
  const mongoose = await connectToMongo();
  return mongoose.connection.collection(collectionName);
}

/**
 * Find documents in a collection using MongoDB native driver
 * Returns an array of documents instead of a cursor
 */
export async function findDocuments(collectionName: string, query: any = {}, options: QueryOptions = {}): Promise<any[]> {
  const collection = await getMongoCollection(collectionName);
  return collection.find(query, options).toArray();
}

/**
 * Insert documents into a collection using MongoDB native driver
 */
export async function insertDocuments(collectionName: string, documents: any[]): Promise<any> {
  const collection = await getMongoCollection(collectionName);
  return collection.insertMany(documents);
}

/**
 * Update documents in a collection using MongoDB native driver
 */
export async function updateDocuments(collectionName: string, filter: any, update: any, options: any = {}): Promise<any> {
  const collection = await getMongoCollection(collectionName);
  return collection.updateMany(filter, update, options);
}

/**
 * Delete documents from a collection using MongoDB native driver
 */
export async function deleteDocuments(collectionName: string, filter: any): Promise<any> {
  const collection = await getMongoCollection(collectionName);
  return collection.deleteMany(filter);
}
