import { findDocuments } from "../database/db.module";

export const getCityMap = async () => {
    try {
        const cities = await findDocuments('cities', {});
        const map = new Map();

        cities.forEach(city => {
            map.set(city.name, city._id);
            map.set(city.name.toLowerCase(), city._id);
            map.set(city._id.toString(), city);
        });

        return map;
    } catch (error) {
        console.error('Error fetching city data:', error);
        throw error;
    }
};
