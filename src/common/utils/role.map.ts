import { findDocuments } from "../database/db.module";

export const getRoleMap = async () => {
    try {
        const roles = await findDocuments('roles', {});
        const map = new Map();

        roles.forEach(role => {
            if (role.type == 'superAdmin') {
                return;
            }
            map.set(role.type, role._id);
        });
        if (map.size === 0) {
            throw new Error('No roles found');
        }
        return map;
    } catch (error) {
        console.error('Error fetching role data:', error);
        throw error;
    }
};
