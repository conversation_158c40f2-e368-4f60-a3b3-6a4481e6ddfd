import { Types } from 'mongoose';
import validator from 'validator';
import { User } from '../user/user.model';
import { Facility } from '../facility/facility.model';
import { Pricing } from '../pricing/pricing.model';
import { ICsvInvoice } from './invoice.interface';
import { Client } from '../user/client.model';
import { PaymentMethod } from './payment-method.model';
import { CustomPackage } from '../pricing/custom-package.model';
import Product from '../products/products.model';
import { Inventory } from '../products/inventory.model';


export async function getAllUsers(ids: any[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []
  const email: string[] = []
  const mobile: string[] = []

  ids.forEach(idStr => {
    if (Types.ObjectId.isValid(`${idStr}`)) {
      _id.push(new Types.ObjectId(idStr));
      return;
    };
    if (validator.isEmail(idStr)) {
      email.push(idStr);
      return;
    }
    if (validator.isMobilePhone(idStr) && idStr.length > 10) {
      mobile.push(idStr);
      return;
    }
    id.push(idStr);
  });
  const users = await User.find({
    organizationId: global.config.organizationId,
    $or: [
      { _id: { $in: _id } },
      { invoiceItemId: { $in: id } },
      { email: { $in: email } },
      { mobile: { $in: mobile } },
    ]
  },).exec();
  return users;
}

export async function getAllClients(ids: any[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []

  ids.forEach(idstr => {
    if (Types.ObjectId.isValid(`${idstr}`)) {
      _id.push(new Types.ObjectId(idstr));
      return;
    };
    id.push(idstr);
  });
  const clients = await Client.find({
    $or: [
      { userId: { $in: _id } },
      { userId: { $in: id } },
    ]
  }).exec();
  return clients;
}

export async function getAllFacility(params: any[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []

  params.forEach(idstr => {
    if (Types.ObjectId.isValid(`${idstr}`)) {
      _id.push(new Types.ObjectId(idstr));
      return;
    };
    id.push(idstr);
  });
  const facilities = await Facility.find({
    $or: [
      { _id: { $in: _id } },
      { invoiceItemId: { $in: id } },
    ]
  }, { _id: 1, invoiceItemId: 1 }).exec();
  return facilities;

}

export async function getAllPricing(params: any[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []

  params.forEach(idstr => {
    if (Types.ObjectId.isValid(`${idstr}`)) {
      _id.push(new Types.ObjectId(idstr));
      return;
    };
    id.push(idstr);
  });
  const pricings = await Pricing.find({
    $or: [
      { _id: { $in: _id } },
      { invoiceItemId: { $in: id } },
    ]
  }).exec();

  return pricings;
}

export async function getAllCustomPackage(params: any[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []

  params.forEach(idstr => {
    if (Types.ObjectId.isValid(`${idstr}`)) {
      _id.push(new Types.ObjectId(idstr));
      return;
    };
    id.push(idstr);
  });
  const customPackages = await CustomPackage.find({
    $or: [
      { _id: { $in: _id } },
      { invoiceItemId: { $in: id } },
    ]
  }).exec();
  return customPackages;
}

export async function getAllProduct(params: any[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []

  params.forEach(idstr => {
    if (Types.ObjectId.isValid(`${idstr}`)) {
      _id.push(new Types.ObjectId(idstr));
      return;
    };
    id.push(idstr);
  });
  const products = await Product.find({
    $or: [
      { _id: { $in: _id } },
      { invoiceItemId: { $in: id } },
    ]
  }).exec();
  return products;
}

export async function getAllInventory(params: any[]) {
  const _id: Types.ObjectId[] = []
  const id: string[] = []
  const productId: Types.ObjectId[] = []

  params.forEach(idstr => {
    if (Types.ObjectId.isValid(`${idstr}`)) {
      _id.push(new Types.ObjectId(idstr));
      return;
    };
    id.push(idstr);
  });
  const inventories = await Inventory.find({
    $or: [
      { _id: { $in: _id } },
      { invoiceItemId: { $in: id } },
      { productId: { $in: productId } },
    ]
  }).exec();
  return inventories;
}

export async function getAllPaymentMethod(params: any[]) {
  const shortId: string[] = params
  const paymentMethods = await PaymentMethod.find({
    shortId: { $in: shortId }
  });
  return paymentMethods;
}

/**
 * Validate invoice data
 * @param invoice Invoice data to validate
 * @returns Array of validation errors
 */
function validateInvoiceData(invoice: ICsvInvoice): string[] {
  const errors: string[] = [];

  if (!invoice.invoiceId) errors.push('Invoice ID is required');
  if (!invoice.userId) errors.push('User ID is required');
  if (!invoice.facilityId) errors.push('Facility ID is required');
  if (!invoice.invoiceDate) errors.push('Invoice date is required');
  if (invoice.amountPaid === undefined) errors.push('Subtotal is required')
  if (invoice.paymentMethod === undefined) errors.push('Cart discount is required');
  if (!invoice.cartDiscountType) errors.push('Cart discount type is required');

  return errors;
}
