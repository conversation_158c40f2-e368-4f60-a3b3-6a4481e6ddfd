import mongoose, { Document, Schema, SchemaTypes, Types } from 'mongoose';
import { PaymentStatus } from '../../utils/enums/payment.enum';
import { InvoiceStatus } from '../../utils/enums/invoice-status.enum';
import { PaymentMethod } from '../../utils/enums/paymentMethod.enum';
import { ENUM_DISCOUNT_TYPE, ENUM_DURATION_UNIT, ENUM_PAYMENT_STATUS, ENUM_PRODUCT_ITEM_TYPE } from '../../common/enums/enums';

export interface IInvoicePurchaseItem {
  packageId: Types.ObjectId;
  packageName: string;
  purchaseIds: Types.ObjectId[];
  quantity: number;
  isBundledPricing?: boolean;
  expireIn: number;
  durationUnit: ENUM_DURATION_UNIT;
  startDate: Date;
  endDate: Date;
  price: number;
  unitPrice: number;
  discountType?: ENUM_DISCOUNT_TYPE;
  discountValue?: number;
  discountedBy?: Types.ObjectId;
  discountExcludeCart: number;
  discountIncludeCart: number;
  returnDiscountAmount?: number;
  voucherDiscountAmount?: number;
  finalPrice: number;
  hsnOrSacCode?: string;
  tax: number;
  gstAmount: number;
  promotionLabel?: string;
  promotionLabelKey?: string;
}

export interface IInvoiceProductItem {
  productId: Types.ObjectId;
  productVariantId?: Types.ObjectId;
  productName: string;
  quantity: number;
  price: number;
  unitPrice: number;
  salePrice: number;
  finalPrice: number;
  discountType?: string;
  discountValue?: number;
  discountedBy?: Types.ObjectId;
  discountExcludeCart: number;
  discountIncludeCart: number;
  returnDiscountAmount?: number;
  voucherDiscountAmount?: number;
  hsnOrSacCode?: string;
  tax: number;
  gstAmount: number;
  promotionLabel?: string;
  promotionLabelKey?: string;
}

export interface IInvoiceCustomPackageItem {
  customPackageId: Types.ObjectId;
  packageName: string;
  quantity: number;
  price: number;
  unitPrice: number;
  discountType?: ENUM_DISCOUNT_TYPE;
  discountValue?: number;
  discountExcludeCart: number;
  discountIncludeCart: number;
  returnDiscountAmount?: number;
  voucherDiscountAmount?: number;
  finalPrice: number;
  hsnOrSacCode?: string;
  tax: number;
  gstAmount: number;
}

export interface IPaymentDetail {
  paymentMethod: string;
  paymentMethodId: Types.ObjectId;
  paymentGateway?: string;
  transactionId?: string;
  amount: number;
  paymentDate: Date;
  paymentStatus?: ENUM_PAYMENT_STATUS;
  description?: string;
  denominations?: Record<number, number>;
}

export interface IClientDetails {
  customerId: Types.ObjectId;
  name: string;
  email?: string;
  phone?: string;
}

export interface IClientBillingDetails {
  customerId: Types.ObjectId;
  name: string;
  addressLine1?: string;
  addressLine2?: string;
  postalCode?: number;
  cityId?: Types.ObjectId;
  cityName?: string;
  stateId: Types.ObjectId;
  stateName: string;
  email?: string;
  phone?: string;
  gstNumber?: string;
  utCode: string;
}

export interface IBillingDetails {
  facilityName: string;
  billingName: string;
  gstNumber?: string;
  email: string;
  phone: string;
  addressLine1: string;
  addressLine2?: string;
  postalCode?: number;
  cityId: Types.ObjectId;
  cityName: string;
  stateId: Types.ObjectId;
  stateName: string;
  utCode: string;
}

export interface IInvoiceReturnItems {
  invoiceId: Types.ObjectId;
  packageId: Types.ObjectId;
  purchaseId: Types.ObjectId;
  packageName: string;
  quantity: number;
  expireIn?: number;
  durationUnit?: ENUM_DURATION_UNIT;
  startDate?: Date;
  endDate?: Date;
  unitPrice: number;
  price: number;
  tax: number;
  hsnOrSacCode?: string;
  isInclusiveGst: boolean;
  basePrice: number;
  gstAmount: number;
  totalPrice: number;
  itemType: ENUM_PRODUCT_ITEM_TYPE;
}

export interface IInvoice extends Document {
  createdBy: Types.ObjectId;
  cancelledBy?: Types.ObjectId;
  invoiceDate: Date;
  invoiceNumber: number;
  orderId: number;
  userId: Types.ObjectId;
  organizationId: Types.ObjectId;
  facilityId: Types.ObjectId;
  purchaseItems: IInvoicePurchaseItem[];
  productItem: IInvoiceProductItem[];
  customPackageItems: IInvoiceCustomPackageItem[];
  isInclusiveofGst: boolean;
  subTotal: number;
  itemDiscount: number;
  returnDiscount: number;
  voucherDiscount: number;
  discount?: number;
  discountedBy?: Types.ObjectId;
  cartDiscount?: number;
  cartDiscountAmount?: number;
  cartDiscountType?: string;
  totalGstValue: number;
  totalAmountAfterGst: number;
  roundOff: number;
  grandTotal: number;
  amountPaid?: number;
  amountInWords: string;
  paymentStatus: PaymentStatus;
  paymentReason?: string;
  paymentDetails: IPaymentDetail[];
  isSplittedPayment: boolean;
  platform?: string;
  invoiceStatus?: string;
  refundStatus?: string;
  refundAmount?: number;
  invoiceFilePath?: string;
  clientDetails: IClientDetails;
  clientBillingDetails: IClientBillingDetails;
  billingDetails: IBillingDetails;
  isForBusiness: boolean;
  paymentBy?: Types.ObjectId;
  voucherDetails?: {
    voucherIds: Types.ObjectId[];
    totalVoucherAmount: number;
    usedVoucherAmount: number;
    remainingVoucherAmount: number;
    appliedOn?: Date;
    vouchers: {
      _id: Types.ObjectId;
      name: string;
      amount: number;
    }[];
  };
  returnItems?: IInvoiceReturnItems[];
  totalReturnBaseAmount?: number;
  totalReturnGstAmount?: number;
  totalReturnValue?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Define schemas for nested objects
const PurchaseItemSchema = new Schema<IInvoicePurchaseItem>({
  packageId: { type: Schema.Types.ObjectId, required: true, ref: "Pricing" },
  purchaseIds: { type: [SchemaTypes.ObjectId], required: true, ref: "Purchase" },
  packageName: { type: String, required: true },
  quantity: { type: Number, required: true, default: 1 },
  isBundledPricing: { type: Boolean, required: false },
  expireIn: { type: Number, required: true },
  durationUnit: { type: String, required: true, enum: Object.values(ENUM_DURATION_UNIT) },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  price: { type: Number, required: true },
  unitPrice: { type: Number, required: true },
  discountType: { type: String, required: false, enum: Object.values(ENUM_DISCOUNT_TYPE) },
  discountedBy: { type: Schema.Types.ObjectId, required: false, default: null },
  discountValue: { type: Number, required: false },
  discountExcludeCart: { type: Number, required: true },
  discountIncludeCart: { type: Number, required: true },
  returnDiscountAmount: { type: Number, required: false },
  voucherDiscountAmount: { type: Number, required: false },
  hsnOrSacCode: { type: String, required: false },
  finalPrice: { type: Number, required: false, select: false },
  tax: { type: Number, required: true, default: 0 },
  gstAmount: { type: Number, required: true, default: 0 },
  promotionLabel: { type: String, required: false },
  promotionLabelKey: { type: String, required: false },
}, { _id: false, timestamps: false });


const ProductItemSchema = new Schema<IInvoiceProductItem>({
  productId: { type: Schema.Types.ObjectId, required: true, ref: 'Product' },
  productVariantId: { type: Schema.Types.ObjectId, required: false, ref: 'ProductVariant' },
  productName: { type: String, required: true },
  quantity: { type: Number, required: true, default: 1 },
  price: { type: Number, required: true },
  unitPrice: { type: Number, required: true },
  salePrice: { type: Number, required: true },
  finalPrice: { type: Number, required: true },
  discountType: { type: String, required: false, default: "percentage" },
  discountValue: { type: Number, required: false },
  discountedBy: { type: Schema.Types.ObjectId, required: false, default: null },
  discountExcludeCart: { type: Number, required: true },
  discountIncludeCart: { type: Number, required: true },
  returnDiscountAmount: { type: Number, required: false },
  voucherDiscountAmount: { type: Number, required: false },
  hsnOrSacCode: { type: String, required: false },
  tax: { type: Number, required: true, default: 0 },
  gstAmount: { type: Number, required: true, default: 0 },
  promotionLabel: { type: String, required: false },
  promotionLabelKey: { type: String, required: false },
}, { _id: false });

const CustomPackageItemSchema = new Schema<IInvoiceCustomPackageItem>({
  customPackageId: { type: Schema.Types.ObjectId, required: true, ref: "CustomPackage" },
  packageName: { type: String, required: true },
  quantity: { type: Number, required: true, default: 1 },
  price: { type: Number, required: true },
  unitPrice: { type: Number, required: true },
  discountType: { type: String, required: false, enum: Object.values(ENUM_DISCOUNT_TYPE) },
  discountValue: { type: Number, required: false },
  discountExcludeCart: { type: Number, required: true },
  discountIncludeCart: { type: Number, required: true },
  returnDiscountAmount: { type: Number, required: false },
  voucherDiscountAmount: { type: Number, required: false },
  hsnOrSacCode: { type: String, required: false },
  finalPrice: { type: Number, required: false, select: false },
  tax: { type: Number, required: true, default: 0 },
  gstAmount: { type: Number, required: true, default: 0 },
}, { _id: false });

export const PaymentDetailSchema = new Schema<IPaymentDetail>({
  paymentMethod: { type: String, required: true },
  paymentMethodId: { type: Schema.Types.ObjectId, required: true, ref: "Facility.paymentMethodId" },
  paymentGateway: { type: String, required: false },
  transactionId: { type: String, required: false },
  amount: { type: Number, required: true },
  paymentDate: { type: Date, required: true },
  paymentStatus: { type: String, required: false, enum: Object.values(PaymentStatus) },
  description: { type: String, required: false },
  denominations: {
    type: Map,
    of: Number,
    required: false,
  },
}, { _id: false, timestamps: false });

const ClientDetailsSchema = new Schema<IClientDetails>({
  customerId: { type: Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  email: { type: String, required: false },
  phone: { type: String, required: false },
}, { _id: false });

const ClientBillingDetailsSchema = new Schema<IClientBillingDetails>({
  customerId: { type: Schema.Types.ObjectId, required: true },
  name: { type: String, required: true },
  addressLine1: { type: String, required: false },
  addressLine2: { type: String, required: false },
  postalCode: { type: Number, required: false },
  cityId: { type: Schema.Types.ObjectId, ref: "Cities", required: false },
  cityName: { type: String, required: false },
  stateId: { type: Schema.Types.ObjectId, ref: "State", required: true },
  stateName: { type: String, required: true },
  email: { type: String, required: false },
  phone: { type: String, required: false },
  gstNumber: { type: String, required: function (this: any) { return this.isForBusiness === true; }, default: "" },
  utCode: { type: String, required: true },
}, { _id: false });

const BillingDetailsSchema = new Schema<IBillingDetails>({
  facilityName: { type: String, required: true },
  billingName: { type: String, required: true },
  gstNumber: { type: String, required: false },
  email: { type: String, required: true },
  phone: { type: String, required: true },
  addressLine1: { type: String, required: false },
  addressLine2: { type: String, required: false },
  postalCode: { type: Number, required: false },
  cityId: { type: Schema.Types.ObjectId, ref: "Cities", required: true },
  cityName: { type: String, required: true },
  stateId: { type: Schema.Types.ObjectId, ref: "State", required: true },
  stateName: { type: String, required: true },
  utCode: { type: String, required: true },
}, { _id: false });

const ReturnItemSchema = new Schema<IInvoiceReturnItems>({
  invoiceId: { type: Schema.Types.ObjectId, required: true },
  packageId: { type: Schema.Types.ObjectId, required: true },
  purchaseId: { type: Schema.Types.ObjectId, required: false },
  packageName: { type: String, required: true },
  quantity: { type: Number, required: true },
  expireIn: { type: Number, required: false },
  durationUnit: { type: String, required: false, enum: Object.values(ENUM_DURATION_UNIT) },
  startDate: { type: Date, required: false },
  endDate: { type: Date, required: false },
  unitPrice: { type: Number, required: true },
  price: { type: Number, required: true },
  tax: { type: Number, required: true },
  hsnOrSacCode: { type: String, required: false },
  isInclusiveGst: { type: Boolean, required: true },
  basePrice: { type: Number, required: true },
  gstAmount: { type: Number, required: true },
  totalPrice: { type: Number, required: true },
  itemType: { type: String, required: true, enum: Object.values(ENUM_PRODUCT_ITEM_TYPE) },
}, { timestamps: false, _id: false });

// Define the main Invoice schema
const InvoiceSchema = new Schema<IInvoice>({
  createdBy: { type: Schema.Types.ObjectId, required: true, ref: "User" },
  cancelledBy: { type: Schema.Types.ObjectId, required: false, ref: 'User' },
  invoiceDate: { type: Date, required: true, default: Date.now },
  invoiceNumber: { type: Number, required: true },
  orderId: { type: Number, required: true },
  userId: { type: Schema.Types.ObjectId, required: true, ref: "User" },
  organizationId: { type: Schema.Types.ObjectId, required: true, ref: "User" },
  facilityId: { type: Schema.Types.ObjectId, required: true, ref: "Facility" },
  purchaseItems: { type: [PurchaseItemSchema], required: true },
  productItem: { type: [ProductItemSchema], required: true },
  customPackageItems: { type: [CustomPackageItemSchema], required: true },
  subTotal: { type: Number, required: true },
  itemDiscount: { type: Number, required: false, default: 0 },
  returnDiscount: { type: Number, required: false, default: 0 },
  voucherDiscount: { type: Number, required: false, default: 0 },
  discount: { type: Number, required: false, default: 0 },
  discountedBy: { type: Schema.Types.ObjectId, required: false, default: null, ref: "User" },
  cartDiscount: { type: Number, required: false, default: 0 },
  cartDiscountAmount: { type: Number, required: false, default: 0 },
  cartDiscountType: { type: String, required: false },
  totalGstValue: { type: Number, required: false, default: 0 },
  totalAmountAfterGst: { type: Number, required: false, default: 0 },
  roundOff: { type: Number, required: false, default: 0 },
  grandTotal: { type: Number, required: true },
  amountPaid: { type: Number, required: false },
  amountInWords: { type: String, required: true },
  paymentStatus: { type: String, required: true, enum: Object.values(PaymentStatus), default: PaymentStatus.COMPLETED },
  paymentReason: { type: String, required: false },
  paymentDetails: { type: [PaymentDetailSchema], required: function (this: any) { return this.paymentStatus === PaymentStatus.COMPLETED; } },
  isSplittedPayment: { type: Boolean, required: false, default: false },
  platform: { type: String, required: false },
  invoiceStatus: { type: String, required: false, default: InvoiceStatus.PENDING },
  refundStatus: { type: String, required: false },
  refundAmount: { type: Number, required: false },
  invoiceFilePath: { type: String, required: false },
  clientDetails: { type: ClientDetailsSchema, required: true },
  clientBillingDetails: { type: ClientBillingDetailsSchema, required: true },
  billingDetails: { type: BillingDetailsSchema, required: true },
  isForBusiness: { type: Boolean, default: false, required: false },
  isInclusiveofGst: { type: Boolean, default: true, required: false },
  returnItems: {
    type: [ReturnItemSchema],
    required: false,
    default: [],
  },
  voucherDetails: {
    type: {
      voucherIds: [{ type: SchemaTypes.ObjectId, ref: 'PurchasedPackages', required: true }],
      totalVoucherAmount: { type: Number, required: true },
      usedVoucherAmount: { type: Number, required: true },
      remainingVoucherAmount: { type: Number, required: true },
      appliedOn: { type: Date, required: true, default: Date.now },
      // vouchers: [{
      //   _id: { type: SchemaTypes.ObjectId, ref: 'PurchasedPackages', required: true },
      //   name: { type: String, required: true },
      //   amount: { type: Number, required: true }
      // }]
    },
    required: false,
    default: undefined,
  },
  totalReturnBaseAmount: { type: Number, required: false, default: 0 },
  totalReturnGstAmount: { type: Number, required: false, default: 0 },
  totalReturnValue: { type: Number, required: false, default: 0 },
  paymentBy: { type: Schema.Types.ObjectId, default: null, required: false },
}, { timestamps: true });

// Create and export the model
export const INVOICE_COLLECTION = 'invoices';
export const Invoice = mongoose.model<IInvoice>('Invoice', InvoiceSchema, INVOICE_COLLECTION);

InvoiceSchema.index({ id: 1, organizationId: 1 }, { unique: true, sparse: true });

InvoiceSchema.pre('save', function (next) {
  this.purchaseItems.forEach(item => {
    item.finalPrice = undefined;
  });
  this.customPackageItems.forEach(item => {
    item.finalPrice = undefined;
  });
  next();
});
