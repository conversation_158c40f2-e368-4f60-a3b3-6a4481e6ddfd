import { Types } from 'mongoose';
import { MigrationTracker, MigrationStatus, MigrationType, IMigrationDetail } from './migration-tracker.model';
import LoggerConfig from '../../common/logger/log.module';

const logger = LoggerConfig('migration-tracker.service');

/**
 * Service for tracking migrations
 */
export class MigrationTrackerService {
  /**
   * Start a new migration
   * @param organizationId Organization ID
   * @param migrationType Type of migration
   * @param userId User ID who initiated the migration
   * @returns Migration tracker document
   */
  static async startMigration(
    organizationId: string,
    migrationType: MigrationType,
    userId: string
  ) {
    try {
      const tracker = new MigrationTracker({
        organizationId: new Types.ObjectId(organizationId),
        migrationType,
        status: MigrationStatus.IN_PROGRESS,
        startTime: new Date(),
        details: [],
        createdBy: new Types.ObjectId(userId)
      });

      await tracker.save();
      logger.log(`Started migration tracking for ${migrationType} in organization ${organizationId}`);
      return tracker;
    } catch (error) {
      logger.error('Error starting migration tracking:', error);
      throw error;
    }
  }

  /**
   * Update migration details for a module
   * @param trackerId Migration tracker ID
   * @param moduleDetail Module migration details
   * @returns Updated migration tracker
   */
  static async updateMigrationDetail(
    trackerId: string,
    moduleDetail: IMigrationDetail
  ) {
    try {
      const tracker = await MigrationTracker.findById(trackerId);
      if (!tracker) {
        throw new Error(`Migration tracker with ID ${trackerId} not found`);
      }

      // Check if module already exists in details
      const existingDetailIndex = tracker.details.findIndex(
        detail => detail.module === moduleDetail.module
      );

      if (existingDetailIndex >= 0) {
        // Update existing module detail
        tracker.details[existingDetailIndex] = moduleDetail;
      } else {
        // Add new module detail
        tracker.details.push(moduleDetail);
      }

      await tracker.save();
      logger.log(`Updated migration details for module ${moduleDetail.module}`);
      return tracker;
    } catch (error) {
      logger.error('Error updating migration details:', error);
      throw error;
    }
  }

  /**
   * Complete a migration
   * @param trackerId Migration tracker ID
   * @param status Final status of the migration
   * @returns Updated migration tracker
   */
  static async completeMigration(
    trackerId: string,
    status: MigrationStatus = MigrationStatus.COMPLETED
  ) {
    try {
      const tracker = await MigrationTracker.findById(trackerId);
      if (!tracker) {
        throw new Error(`Migration tracker with ID ${trackerId} not found`);
      }

      tracker.status = status;
      tracker.endTime = new Date();
      await tracker.save();

      logger.log(`Completed migration ${trackerId} with status ${status}`);
      return tracker;
    } catch (error) {
      logger.error('Error completing migration:', error);
      throw error;
    }
  }

  /**
   * Get migration history for an organization
   * @param organizationId Organization ID
   * @param limit Number of records to return
   * @returns List of migration trackers
   */
  static async getMigrationHistory(
    organizationId: string,
    limit: number = 10
  ) {
    try {
      const history = await MigrationTracker.find({ organizationId: new Types.ObjectId(organizationId) })
        .sort({ createdAt: -1 })
        .limit(limit);

      return history;
    } catch (error) {
      logger.error('Error getting migration history:', error);
      throw error;
    }
  }

  /**
   * Get the latest migration of a specific type for an organization
   * @param organizationId Organization ID
   * @param migrationType Type of migration
   * @returns Latest migration tracker
   */
  static async getLatestMigration(
    organizationId: string,
    migrationType: MigrationType
  ) {
    try {
      const latest = await MigrationTracker.findOne({
        organizationId: new Types.ObjectId(organizationId),
        migrationType
      }).sort({ createdAt: -1 });

      return latest;
    } catch (error) {
      logger.error('Error getting latest migration:', error);
      throw error;
    }
  }
}
