
import mongoose, { Schema } from 'mongoose';
import { Document } from 'mongoose';
import { IRevenueCategory, RevenueCategorySchema, RevenueCategory } from './revenue-category.model';

export interface IOrganization extends Document {
    userId: mongoose.Types.ObjectId;
    revenueCategory: IRevenueCategory[];
}

const OrganizationSchema = new Schema<IOrganization>({

    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true,
    },

    revenueCategory: {
        type: [RevenueCategorySchema],
        required: false,
        default: []
    }
}, { timestamps: true });

export const Organization = mongoose.model<IOrganization>('organizations', OrganizationSchema);