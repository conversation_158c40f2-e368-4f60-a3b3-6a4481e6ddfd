import { Types } from "mongoose";
import LoggerConfig from "../../common/logger/log.module";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { Pricing, SessionType, ClassType, DurationUnit, IPricing, IActiveTimeFrame } from "./pricing.model";
import { Service } from "./service.model";
import { parseCSV } from "../../common/utils/csv-parser";
import { DaysOfWeek } from "../../utils/enums/days-of-week.enum";
import { IAppointmentType } from "./appointment-type.model";
import { ENUM_PRODUCT_ITEM_TYPE } from "../../common/enums/enums";
import { RevenueCategory } from "../organization/revenue-category.model";
import { Organization } from "../organization/organization.model";
import { MembershipModel } from "../organization/membership.model";

interface ICsvServiceEntry {
    serviceCategory: string;
    appointmentTypes: string[];
    relationShip?: Array<{
        serviceCategory: string;
        subTypeIds: string[];
    }>;
}

interface ICsvPricing {
    id: string;
    name: string;
    price: number;
    tax: number;
    promotion?: string;
    hsnOrSacCode?: string;
    durationUnit: string;
    expiredInDays: number;
    type?: ClassType;
    sessionType?: SessionType;
    sessionCount?: number;
    isSellOnline?: boolean;
    isActive?: boolean;
    isBundledPricing?: boolean;
    pricingIds?: string;
    revenueCategory?: string;
    shortDescription?: string;
    description?: string;
    membership?: string;
    image?: string;
    isTrial?: boolean;
    activeDate1: string;
    activeDay1: DaysOfWeek;
    activeStartTime1: string;
    activeEndTime1: string;
    serviceCategory1: string,
    appointmentType1: string,
    serviceCategory2: string,
    appointmentType2: string,
    serviceCategory3: string,
    appointmentType3: string,
    serviceCategory4: string,
    appointmentType4: string,

    [string: string]: any;
}

const CsvToObjectKeyMapPricing: Record<keyof ICsvPricing, string> = {
    id: "id",
    name: "name",
    price: "price",
    tax: "tax",
    promotion: "promotion id",
    hsnOrSacCode: "hsnorsaccode",
    durationUnit: "expiry unit",
    expiredInDays: "expiry value",
    type: "type",
    sessionType: "session type",
    sessionCount: "session count",
    isSellOnline: "sell online",
    isActive: "is active",
    isBundledPricing: "is bundle",
    pricingIds: "pricing ids",
    revenueCategory: "revenue category",
    shortDescription: "short description",
    description: "description",
    membership: "membership",
    image: "image",
    isTrial: "is trial",
    activeDate1: "active date 1",
    activeDay1: "active day 1",
    activeStartTime1: "active start time 1",
    activeEndTime1: "active end time 1",
    serviceCategory1: "service category 1",
    appointmentType1: "services 1",
    serviceCategory2: "service category 2",
    appointmentType2: "services 2",
    serviceCategory3: "service category 3",
    appointmentType3: "services 3",
    serviceCategory4: "service category 4",
    appointmentType4: "services 4",
}

interface IPricingDto extends Omit<ICsvPricing, "serviceCategory1" | "appointmentType1" | "serviceCategory2" | "appointmentType2" | "serviceCategory3" | "appointmentType3" | "serviceCategory4" | "appointmentType4" | "activeDate1" | "activeDay1" | "activeStartTime1" | "activeEndTime1" | "activeDate2" | "activeDay2" | "activeStartTime2" | "activeEndTime2" | "activeDate3" | "activeDay3" | "activeStartTime3" | "activeEndTime3" | "activeDate4" | "activeDay4" | "activeStartTime4" | "activeEndTime4"> {
    dayPassLimit: number;
    sessionPerDay: number;
    activeTimeFrames: IActiveTimeFrame[];
    services: ICsvServiceEntry;
}


const logger = LoggerConfig('pricing.migration');

/**
 * Validate pricing data from CSV
 * @param pricing Pricing data from CSV
 * @returns Array of validation error messages
 */
function validatePricingData(pricing: IPricingDto): string[] {
    const errors: string[] = [];

    if (!pricing.name) {
        errors.push("Pricing name is required");
    }

    if (pricing.price === undefined) {
        errors.push("Price is required");
    } else if (isNaN(pricing.price) || pricing.price < 0) {
        errors.push("Price must be a non-negative number");
    }

    if (pricing.tax === undefined) {
        errors.push("Tax is required");
    } else if (isNaN(pricing.tax) || pricing.tax < 0) {
        errors.push("Tax must be a non-negative number");
    }

    if (!pricing.type) {
        errors.push("Type is required");
    }

    if (!pricing.sessionType) {
        errors.push("Session type is required");
    }

    if (pricing.sessionCount === undefined) {
        errors.push("Session count is required");
    } else if (isNaN(pricing.sessionCount) || pricing.sessionCount <= 0) {
        errors.push("Session count must be a positive number");
    }

    if (pricing.isSellOnline === undefined) {
        errors.push("isSellOnline flag is required");
    }

    if (pricing.expiredInDays === undefined) {
        errors.push("Expired in days is required");
    } else if (isNaN(pricing.expiredInDays) || pricing.expiredInDays <= 0) {
        errors.push("Expired in days must be a positive number");
    }

    if (!pricing.services) {
        errors.push("Services are required");
    } else {
        if (!pricing.services.serviceCategory) {
            errors.push("Primary service category is required");
        }
        if (!pricing.services.appointmentTypes) {
            errors.push("Primary service appointment types are required");
        }
        if (pricing.services.relationShip) {
            pricing.services.relationShip.forEach((relationship, index) => {
                if (!relationship.serviceCategory) {
                    errors.push(`Additional service category ${index + 1} is required`);
                }
                if (!relationship.subTypeIds) {
                    errors.push(`Additional service appointment types ${index + 1} are required`);
                }
            });
        }
    }

    return errors;
}

function validateBundlePricingData(pricing: IPricingDto) {
    const errors: string[] = [];

    if (!pricing.name) {
        errors.push("Pricing name is required");
    }

    if (pricing.price === undefined) {
        errors.push("Price is required");
    } else if (isNaN(pricing.price) || pricing.price < 0) {
        errors.push("Price must be a non-negative number");
    }

    if (pricing.tax === undefined) {
        errors.push("Tax is required");
    } else if (isNaN(pricing.tax) || pricing.tax < 0) {
        errors.push("Tax must be a non-negative number");
    }

    if (pricing.expiredInDays === undefined) {
        errors.push("Expired in days is required");
    } else if (isNaN(pricing.expiredInDays) || pricing.expiredInDays <= 0) {
        errors.push("Expired in days must be a positive number");
    }

    if (!pricing.pricingIds || !pricing?.pricingIds?.length) {
        errors.push("Pricing ids are required");
    }

    return errors;
}


export async function migratePricing(_dbName: string = "hop-migration"): Promise<void> {
    let session: any = null;

    try {
        logger.log("Starting pricing migration...");

        // Connect to database
        const mongoose = await connectToMongo();

        // Start a session for transaction
        session = await mongoose.startSession();
        session.startTransaction();

        try {
            // Get pricing data from CSV or other source
            const csvPricings = await parseCSV<ICsvPricing>("pricing.csv", CsvToObjectKeyMapPricing);

            // Convert csvPricings to IPricingDto format
            const pricingDtos = csvPricings.map((pricing): IPricingDto => {
                const services: { [key: string]: ICsvServiceEntry } = {};

                // Handle up to 4 service categories and their appointment types

                const primaryService: ICsvServiceEntry = {
                    serviceCategory: pricing.serviceCategory1,
                    appointmentTypes: (pricing.appointmentType1 || "").split(",")
                };
                const relationships: ICsvServiceEntry["relationShip"] = [];
                for (let i = 2; i <= 10; i++) {
                    const serviceCategory = pricing[`serviceCategory${i}`] as string;
                    const appointmentType = pricing[`appointmentType${i}`] as string;
                    if (!serviceCategory) {
                        break;
                    }
                    if (serviceCategory) {
                        relationships.push({
                            serviceCategory: serviceCategory,
                            subTypeIds: (appointmentType || "").split(",")
                        });
                    }
                }

                const activeTimeFrames: IActiveTimeFrame[] = [];
                for (let i = 1; i <= 10; i++) {
                    const activeDate = pricing[`activeDate${i}`] as string;
                    const activeDay = pricing[`activeDay${i}`] as DaysOfWeek;
                    if (!activeDate && !activeDay) {
                        break;
                    }
                    const activeStartTime = pricing[`activeStartTime${i}`] as string;
                    const activeEndTime = pricing[`activeEndTime${i}`] as string;

                    activeTimeFrames.push({
                        date: activeDate ? new Date(activeDate) : null,
                        dayOfWeek: activeDay,
                        startTime: activeStartTime,
                        endTime: activeEndTime
                    });
                }

                let dayPassLimit = 0;
                let sessionPerDay = 0;
                let sessionCount = 0;
                let sessionType = ""
                if ([SessionType.DAY_PASS, "Day_Pass", "day_pass", "Day Pass", "Day pass", "day pass"].includes(pricing.sessionType.trim())) {
                    dayPassLimit = Number(pricing.sessionCount);
                    sessionPerDay = Infinity;
                    sessionCount = Infinity;
                    sessionType = SessionType.DAY_PASS;
                }
                if ([SessionType.MULTIPLE, "Multiple", "multiple"].includes(pricing.sessionType.trim())) {
                    dayPassLimit = 0;
                    sessionPerDay = 0;
                    sessionCount = Number(pricing.sessionCount);
                    sessionType = SessionType.MULTIPLE;
                }
                if ([SessionType.SINGLE, "Single", "single"].includes(pricing.sessionType.trim())) {
                    dayPassLimit = 0;
                    sessionPerDay = 0;
                    sessionCount = 1;
                    sessionType = SessionType.SINGLE;
                }
                if ([SessionType.UNLIMITED, "Unlimited", "unlimited"].includes(pricing.sessionType.trim())) {
                    dayPassLimit = 0;
                    sessionPerDay = Number(pricing.sessionCount);;
                    sessionCount = Infinity;
                    sessionType = SessionType.UNLIMITED;
                }

                return {
                    id: pricing.id,
                    name: pricing.name,
                    price: Number(pricing.price),
                    tax: Number(pricing.tax),
                    hsnOrSacCode: pricing.hsnOrSacCode,
                    durationUnit: pricing.durationUnit,
                    expiredInDays: parseInt(`${pricing.expiredInDays}`),
                    type: pricing.type,
                    sessionType: sessionType,
                    sessionCount: Number(sessionCount),
                    dayPassLimit: dayPassLimit,
                    sessionPerDay: sessionPerDay,
                    isSellOnline: pricing.isSellOnline,
                    isActive: pricing.isActive,
                    pricingIds: pricing.pricingIds?.split(",") || [],
                    isBundledPricing: pricing.isBundledPricing,
                    revenueCategory: pricing.revenueCategory,
                    image: pricing.image,
                    isTrial: pricing.isTrial,
                    promotion: pricing.promotion,
                    description: pricing.description,
                    shortDescription: pricing.shortDescription,
                    activeTimeFrames: activeTimeFrames,
                    services: {
                        ...primaryService,
                        relationShip: relationships
                    }
                };
            });

            // Validate data
            const validationErrors: { [key: string]: string[] } = {};
            pricingDtos.forEach((pricing, index) => {
                if (pricing.isBundledPricing) {
                    const errors = validateBundlePricingData(pricing);
                    if (errors.length > 0) {
                        validationErrors[`Row ${index + 1}`] = errors;
                    }
                    return;
                }
                const errors = validatePricingData(pricing);
                if (errors.length > 0) {
                    validationErrors[`Row ${index + 1}`] = errors;
                }
            });

            if (Object.keys(validationErrors).length > 0) {
                logger.error("Validation errors in pricing data:", validationErrors);
                throw new Error("Invalid pricing data in CSV");
            }

            // Get all services from database to create name to ObjectId mapping
            const dbServices = await Service.find({}).session(session);

            const serviceIdToObjectId: Record<string, typeof Service> | {} = {};
            dbServices.forEach(service => {
                serviceIdToObjectId[service.invoiceItemId] = service;
            });

            // Get all revenue categories from database to create name to ObjectId mapping
            const org = await Organization.findOne({ userId: global.config.organizationId }).session(session);
            const revenueCategory = org.revenueCategory || [];
            const revenueCategoryMapByName: Record<string, string> = {};

            revenueCategory.map((category) => {
                revenueCategoryMapByName[category.name] = category._id.toString();
            });

            // Membership 
            const membership = await MembershipModel.find({ organizationId: global.config.organizationId }).session(session);
            const membershipMapByName: Record<string, string> = {};
            membership.forEach(m => {
                membershipMapByName[m.name] = m._id.toString();
                membershipMapByName[m._id.toString()] = m._id.toString();
                if (m.invoiceItemId) {
                    membershipMapByName[m.invoiceItemId] = m._id.toString();
                }
            });

            // Transform data for MongoDB
            const pricings = [];
            const pricingIdMap: Record<string, string> = {};
            const errors = [];
            const newRevenueCategories = [];

            let i = 0;
            for (const csvPricing of pricingDtos) {
                i++;
                if (csvPricing.isBundledPricing) {
                    continue;
                }

                if (!csvPricing.services) {
                    errors.push(`Row ${i}: No services found for pricing "${csvPricing.name}"`);
                    continue;
                    // throw new Error(`No services found for pricing "${csvPricing.name}"`);
                }

                const service = serviceIdToObjectId[csvPricing.services.serviceCategory];
                if (!service) {
                    errors.push(`Row ${i}: Service not found for pricing "${csvPricing.name}"`);
                    continue;
                    // throw new Error(`Service not found for pricing "${csvPricing.name}"`);
                }
                if (service.classType !== csvPricing.type) {
                    errors.push(`Row ${i}: Service type must be one of "${Object.values(ClassType).join(',')}"`);
                    continue;
                    // throw new Error(`Service type mismatch for pricing "${csvPricing.name}"`);
                }
                const appointmentTypes = service.appointmentType.filter(apt => csvPricing.services.appointmentTypes.includes(apt.id));
                if (appointmentTypes.length !== csvPricing.services.appointmentTypes.length) {
                    errors.push(`Row ${i}: Service (appointment types) not found for pricing "${csvPricing.name}"`);
                    continue;
                    // throw new Error(`Service (appointment types) not found for pricing "${csvPricing.name}"`);
                }

                let membershipId: Types.ObjectId = null;
                if (csvPricing.membership) {
                    if (!membershipMapByName[csvPricing.membership]) {
                        errors.push(`Row ${i}: Membership not found for pricing "${csvPricing.name}"`);
                        continue;
                        // throw new Error(`Membership not found for pricing "${csvPricing.name}"`);
                    }
                    membershipId = new Types.ObjectId(membershipMapByName[csvPricing.membership]);
                }

                if (csvPricing.revenueCategory) {
                    if (!revenueCategoryMapByName[csvPricing.revenueCategory]) {
                        const newRevenueCategory = new RevenueCategory({
                            name: csvPricing.revenueCategory,
                            description: "",
                            isActive: true
                        });
                        newRevenueCategories.push(newRevenueCategory);
                        revenueCategoryMapByName[csvPricing.revenueCategory] = newRevenueCategory._id.toString();
                    }
                }
                const appointmentTypeIds = appointmentTypes.map(apt => apt._id);

                const relationships = []
                csvPricing.services.relationShip?.map(relationship => {
                    const relatedService = serviceIdToObjectId[relationship.serviceCategory];
                    if (!relatedService) {
                        errors.push(`Row ${i}: Related service not found for pricing "${csvPricing.name}" of ${relationship.serviceCategory}`);
                        return;
                    }
                    if (service.classType !== csvPricing.type) {
                        errors.push(`Row ${i}: Service type mismatch for pricing "${csvPricing.name}" of ${relationship.serviceCategory}`);
                        return;
                    }
                    const relatedAppointmentTypes = relatedService.appointmentType.filter(apt => relationship.subTypeIds.includes(apt.id));
                    if (relatedAppointmentTypes.length !== relationship.subTypeIds.length) {
                        errors.push(`Row ${i}: Related service (appointment types) not found for pricing "${csvPricing.name}" of ${relationship.serviceCategory}`);
                        return;
                    }
                    const relatedAppointmentTypeIds = relatedAppointmentTypes.map(apt => apt._id);
                    relationships.push({
                        serviceCategory: new Types.ObjectId(relatedService._id),
                        subTypeIds: relatedAppointmentTypeIds
                    });
                });

                const newPricing = new Pricing({
                    id: csvPricing.id,
                    createdBy: new Types.ObjectId(global.config.organizationId),
                    organizationId: new Types.ObjectId(global.config.organizationId),
                    name: csvPricing.name,
                    price: csvPricing.price,
                    isSellOnline: csvPricing.isSellOnline,
                    tax: csvPricing.tax,
                    itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
                    services: {
                        type: csvPricing.type,
                        serviceCategory: new Types.ObjectId(serviceIdToObjectId[csvPricing.services.serviceCategory]._id),
                        appointmentType: appointmentTypeIds,
                        relationShip: relationships,
                        sessionType: csvPricing.sessionType,
                        sessionCount: csvPricing.sessionCount,
                        dayPassLimit: csvPricing.dayPassLimit,
                        sessionPerDay: csvPricing.sessionPerDay,
                        introductoryOffer: "no"
                    },
                    expiredInDays: csvPricing.expiredInDays,
                    durationUnit: csvPricing.durationUnit,
                    hsnOrSacCode: csvPricing.hsnOrSacCode,
                    membershipId: membershipId, //csvPricing.membershipId,
                    discount: null, //csvPricing.discount,
                    promotion: csvPricing.promotion || null,
                    isActive: csvPricing.isActive,
                    pricingIds: [],
                    isBundledPricing: false,
                    revenueCategory: revenueCategoryMapByName[csvPricing.revenueCategory] || null,
                    activeTimeFrames: csvPricing.activeTimeFrames,
                    description: csvPricing.description,
                    shortDescription: csvPricing.shortDescription,
                    image: csvPricing.image,
                    isFeatured: false,
                    isTrial: Boolean(csvPricing.isTrial),
                });
                pricings.push(newPricing);
                pricingIdMap[csvPricing.id] = newPricing._id.toString();
            };

            // Bundle pricing
            for (const csvPricing of pricingDtos) {
                if (!csvPricing.isBundledPricing) {
                    continue;
                }

                const pricingIds = [];
                i++;
                for (const id of csvPricing.pricingIds) {
                    i++;
                    if (!pricingIdMap[id]) {
                        errors.push(`Row ${i}: Pricing not found for bundled pricing "${csvPricing.name}" of ${id}`);
                    }
                    pricingIds.push(new Types.ObjectId(pricingIdMap[id]));
                }

                const newPricing = new Pricing({
                    id: csvPricing.id,
                    createdBy: new Types.ObjectId(global.config.organizationId),
                    organizationId: new Types.ObjectId(global.config.organizationId),
                    name: csvPricing.name,
                    price: csvPricing.price,
                    isSellOnline: csvPricing.isSellOnline,
                    tax: csvPricing.tax,
                    itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
                    services: null,
                    expiredInDays: csvPricing.expiredInDays,
                    durationUnit: csvPricing.durationUnit,
                    hsnOrSacCode: csvPricing.hsnOrSacCode,
                    membershipId: null, //csvPricing.membershipId,
                    discount: null, //csvPricing.discount,
                    promotion: csvPricing.promotion || null,
                    isActive: csvPricing.isActive || false,
                    pricingIds: pricingIds,
                    isBundledPricing: csvPricing.isBundledPricing,
                    revenueCategory: revenueCategoryMapByName[csvPricing.revenueCategory] || null,
                    activeTimeFrames: csvPricing.activeTimeFrames,
                    description: csvPricing.description,
                    shortDescription: csvPricing.shortDescription,
                    image: csvPricing.image,
                    isFeatured: false,
                    isTrial: Boolean(csvPricing.isTrial),
                });
                pricingIdMap[csvPricing.id] = newPricing._id.toString();
                pricings.push(newPricing);
            };
            if (errors.length > 0) {
                for (const error of errors) {
                    logger.error(error);
                }
                // throw new Error(`Validation errors in pricing data. Total errors: ${errors.length}`);
            }

            if (newRevenueCategories.length > 0) {
                await Organization.updateOne(
                    { userId: global.config.organizationId },
                    { $push: { revenueCategory: { $each: newRevenueCategories } } }
                ).session(session);
            }

            // Insert pricing packages
            const result = await Pricing.insertMany(pricings, { session });
            logger.log(`Successfully migrated ${result.length} pricing packages to MongoDB`);

            // Commit the transaction
            await session.commitTransaction();
            logger.log("Transaction committed successfully");
        } catch (error) {
            // Abort the transaction on error
            if (session) {
                await session.abortTransaction();
            }
            logger.error("Error in migration process:", error);
            throw error;
        }
    } catch (error) {
        logger.error("Error migrating pricing packages:", error);
    } finally {
        // End the session
        if (session) {
            session.endSession();
        }
        // Close the connection
        await closeConnection();
    }
}