import { Types } from "mongoose";
import LoggerConfig from "../../common/logger/log.module";
import { connectToMongo, closeConnection } from "../../common/database/db.module";
import { Pricing } from "./pricing.model";
import { parseCSV } from "../../common/utils/csv-parser";
import { ENUM_PRODUCT_ITEM_TYPE } from "../../common/enums/enums";



interface ICsvPricing {
    id: string;
    name: string;
    price: number;
    hsnOrSacCode?: string;
    durationUnit: string;
    expiredInDays: number;

    [string: string]: any;
}

const CsvToObjectKeyMapPricing: Record<keyof ICsvPricing, string> = {
    id: "id",
    name: "name",
    price: "price",
    hsnOrSacCode: "hsnorsaccode",
    durationUnit: "expiry unit",
    expiredInDays: "expiry value",
}

interface IPricingDto extends ICsvPricing {

}


const logger = LoggerConfig('voucher.migration');

/**
 * Validate pricing data from CSV
 * @param pricing Pricing data from CSV
 * @returns Array of validation error messages
 */
function validatePricingData(pricing: IPricingDto): string[] {
    const errors: string[] = [];

    if (!pricing.name) {
        errors.push("Pricing name is required");
    }

    if (pricing.price === undefined) {
        errors.push("Price is required");
    } else if (isNaN(pricing.price) || pricing.price < 0) {
        errors.push("Price must be a non-negative number");
    }

    return errors;
}



export async function migrateVoucher(_dbName: string = "hop-migration"): Promise<void> {
    let session: any = null;

    try {
        logger.log("Starting voucher migration...");

        // Connect to database
        const mongoose = await connectToMongo();

        // Start a session for transaction
        session = await mongoose.startSession();
        session.startTransaction();

        try {
            // Get pricing data from CSV or other source
            const csvPricings = await parseCSV<ICsvPricing>("voucher.csv", CsvToObjectKeyMapPricing);

            // Convert csvPricings to IPricingDto format
            const pricingDtos = csvPricings.map((pricing): IPricingDto => {
                return {
                    id: pricing.id,
                    name: pricing.name,
                    price: Number(pricing.price),
                    hsnOrSacCode: pricing.hsnOrSacCode,
                    durationUnit: pricing.durationUnit,
                    expiredInDays: parseInt(`${pricing.expiredInDays}`),
                    isSellOnline: pricing.isSellOnline,
                    isActive: pricing.isActive,
                    description: pricing.description,
                    shortDescription: pricing.shortDescription,
                };
            });

            // Validate data
            const validationErrors: { [key: string]: string[] } = {};
            pricingDtos.forEach((pricing, index) => {
                const errors = validatePricingData(pricing);
                if (errors.length > 0) {
                    validationErrors[`Row ${index + 1}`] = errors;
                }
            });

            if (Object.keys(validationErrors).length > 0) {
                logger.error("Validation errors in pricing data:", validationErrors);
                throw new Error("Invalid pricing data in CSV");
            }

            // Transform data for MongoDB
            const pricings = [];
            const pricingIdMap: Record<string, string> = {};
            const errors = [];


            let i = 0;
            for (const csvPricing of pricingDtos) {
                i++;
                const newPricing = new Pricing({
                    id: csvPricing.id,
                    createdBy: new Types.ObjectId(global.config.organizationId),
                    organizationId: new Types.ObjectId(global.config.organizationId),
                    name: csvPricing.name,
                    price: csvPricing.price,
                    isSellOnline: csvPricing.isSellOnline,
                    itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,

                    expiredInDays: csvPricing.expiredInDays,
                    durationUnit: csvPricing.durationUnit,
                    hsnOrSacCode: csvPricing.hsnOrSacCode,

                    isActive: csvPricing.isActive,

                    description: csvPricing.description,
                    shortDescription: csvPricing.shortDescription,
                });
                pricings.push(newPricing);
                pricingIdMap[csvPricing.id] = newPricing._id.toString();
            };

            if (errors.length > 0) {
                for (const error of errors) {
                    logger.error(error);
                }
                // throw new Error(`Validation errors in pricing data. Total errors: ${errors.length}`);
            }

            // Insert pricing packages
            const result = await Pricing.insertMany(pricings, { session });
            logger.log(`Successfully migrated ${result.length} voucher packages to MongoDB`);

            // Commit the transaction
            await session.commitTransaction();
            logger.log("Transaction committed successfully");
        } catch (error) {
            // Abort the transaction on error
            if (session) {
                await session.abortTransaction();
            }
            logger.error("Error in migration process:", error);
            throw error;
        }
    } catch (error) {
        logger.error("Error migrating pricing packages:", error);
    } finally {
        // End the session
        if (session) {
            session.endSession();
        }
        // Close the connection
        await closeConnection();
    }
}