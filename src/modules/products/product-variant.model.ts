import mongoose, { Schema, Document, Types } from 'mongoose';
import { ProductAttribute, ProductAttributeSchema } from './product-attribute.model';

export interface ProductVariant extends Document {
    title: string;
    itemCode?: string;
    sku: string;
    productId: Types.ObjectId;
    hsnCode?: string;
    attributes?: ProductAttribute[];
    status?: boolean;
    createdBy?: Types.ObjectId;
    organizationId?: Types.ObjectId;
}

const ProductVariantSchema = new Schema<ProductVariant>(
    {
        title: { type: String, required: true, index: 'text' },
        itemCode: { type: String },
        sku: { type: String, required: true, index: 'text' },
        productId: { type: Schema.Types.ObjectId, ref: 'Product', required: true, index: true },
        hsnCode: { type: String },
        attributes: { type: [ProductAttributeSchema], default: [] },
        status: { type: Boolean, default: true },
        createdBy: { type: Schema.Types.ObjectId, ref: 'User' },
        organizationId: { type: Schema.Types.ObjectId, ref: 'User' },
    },
    {
        timestamps: true,
    }
);

// Duplicate SKU error handling
ProductVariantSchema.post('save', function (error: any, doc: any, next: any) {
    if (error.name === 'MongoServerError' && error.code === 11000) {
        next(new Error('SKU code must be unique for Variant.'));
    } else {
        next(error);
    }
});

const ProductVariantModel = mongoose.model<ProductVariant>('ProductVariant', ProductVariantSchema);

export default ProductVariantModel;
