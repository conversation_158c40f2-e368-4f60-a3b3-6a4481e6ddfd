import mongoose, { Schema, Document, Types } from 'mongoose';
import { Attributes } from '../../common/enums/enums';
import { ProductVariant } from './product-variant.model';
import { ProductAttribute, ProductAttributeSchema } from './product-attribute.model';
import { VariantAttributes } from './variant-attributes.constant';

export enum ProductType {
    SIMPLE = "simple",
    VARIABLE = "variable",
}

export interface IProduct extends Document {
    name: string;
    itemCode: string;
    sku: string;
    slug: string;
    firstCategoryId: Types.ObjectId;
    secondCategoryId?: Types.ObjectId;
    hsn: string;
    mfgCode?: string;
    gst: number;
    introduction?: string;
    description?: string;
    type: ProductType;
    attributes?: ProductAttribute[];
    variantIds?: Types.ObjectId[];
    variantAttributesList?: Attributes[];
    status?: boolean;
    createdBy?: Types.ObjectId;
    organizationId?: Types.ObjectId;
    populatedVariants?: ProductVariant[];
}


const ProductSchema = new Schema<IProduct>(
    {
        name: { type: String, required: true, index: 'text' },
        itemCode: { type: String, required: true },
        sku: { type: String, required: true },
        slug: { type: String, required: true },
        firstCategoryId: { type: Schema.Types.ObjectId, required: true, ref: 'Category', index: true },
        secondCategoryId: { type: Schema.Types.ObjectId, ref: 'Category', index: true },
        hsn: { type: String, required: true },
        mfgCode: { type: String },
        gst: { type: Number, required: true },
        introduction: { type: String },
        description: { type: String },
        type: { type: String, enum: Object.values(ProductType), required: true },
        attributes: { type: [ProductAttributeSchema], default: [] },
        variantIds: [{ type: Schema.Types.ObjectId, ref: 'Variant', index: true }],
        variantAttributesList: { type: [VariantAttributes], default: [] },
        status: { type: Boolean, default: false },
        createdBy: { type: Schema.Types.ObjectId, ref: 'User' },
        organizationId: { type: Schema.Types.ObjectId, ref: 'User' },
    },
    {
        timestamps: true,
    }
);

// Handle duplicate itemCode
ProductSchema.post('save', function (error: any, doc: any, next: any) {
    if (error.name === 'MongoServerError' && error.code === 11000) {
        next(new Error('Item code must be unique. This itemCode already exists.'));
    } else {
        next(error);
    }
});

const Product = mongoose.model<IProduct>('Product', ProductSchema);

export default Product;
