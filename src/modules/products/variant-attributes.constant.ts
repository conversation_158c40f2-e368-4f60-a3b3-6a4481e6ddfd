import { Attributes } from '../../common/enums/enums';
import { InputFieldType } from '../../common/enums/enums';
import { AttributeType } from "../../common/enums/enums";

export const VariantAttributes: Array<Attributes> = [
    Attributes.Shade,
    Attributes.SizeInGM,
    Attributes.SizeInKG,
    Attributes.SizeInL,
    Attributes.SizeInML,
    Attributes.SizeInUnit,
    Attributes.SizeInTablet,
    Attributes.SizeInCapsule,
    // Attributes.Ingredients,
    // Attributes.Dosage,
    Attributes.SkinTone,
    Attributes.HairType,
    Attributes.SkinType,
    Attributes.Packaging,
    Attributes.ProductFormQuantity
];

type AttributeList = {
    [key in Attributes]: {
        name: string;
        type: AttributeType;
        inputType: InputFieldType;
        multiple: boolean;
        fields: Array<{
            name: string;
            type: "text" | "textEditor" | "color" | "image";
            multiple: boolean;
            required: boolean;
        }>;
    };
};

export const AttributeList: AttributeList = {
    brand: {
        name: "Brand",
        type: AttributeType.Mandatory,
        inputType: InputFieldType.Dropdown,
        multiple: false, //cannot select multiple values if multiple is false
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
            {
                name: "description",
                type: "textEditor",
                multiple: false,
                required: false,
            },
            {
                name: "image",
                type: "image",
                multiple: false,
                required: true,
            },
            {
                name: "address",
                type: "text",
                multiple: false,
                required: false,
            },
        ],
    },
    shade: {
        name: "Shade",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
            {
                name: "hexCode",
                type: "color",
                multiple: false,
                required: true,
            },
        ],
    },
    productForm: {
        name: "Product Form", //like Syrup, Tablet, Inhaler etc
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    packageType: {
        name: "Package Type", //like Bottle, Box, Strip etc
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    productTags: {
        name: "Product Tags",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    kg: {
        name: "Size In KG",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    gm: {
        name: "Size In GM",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    litre: {
        name: "Size In Litre",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    ml: {
        name: "Size In ML",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    unit: {
        name: "Size In Unit",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    tablet: {
        name: "Size In Tablet",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    capsule: {
        name: "Size In Capsule",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    ingredients: {
        name: "Ingredients",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    dosage: {
        name: "Dosage",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    gender: {
        name: "Gender",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    ageGroup: {
        name: "Age Group",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    hairType: {
        name: "Hair Type",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    skinType: {
        name: "Skin Type",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    skinTone: {
        name: "Skin Tone",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    occasion: {
        name: "Occasion",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    finish: {
        name: "Finish",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    concern: {
        name: "Concern",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    spf: {
        name: "spf",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    coverage: {
        name: "coverage",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    fragranceFamily: {
        name: "Fragrance Family",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    uses: {
        name: "Uses",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    originCountry: {
        name: "Country Of Origin",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    preference: {
        name: "Preference",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    conscious: {
        name: "Conscious",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    commonSideEffects: {
        name: "Common Side Effects",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: true,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    alcoholInteraction: {
        name: "Alcohol Interaction",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    pregnancyInteraction: {
        name: "Pregnancy Interaction",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    lactationInteraction: {
        name: "Lactation Interaction",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    drivingInteraction: {
        name: "Driving Interaction",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    kidneyInteraction: {
        name: "Kidney Interaction",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    liverInteraction: {
        name: "Liver Interaction",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    medicineType: {
        name: "Medicine Type",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    storage: {
        name: "Storage",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    quantity: {
        name: "Product form Quantity",
        type: AttributeType.Variant,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    factBox: {
        name: "Fact Box",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    packaging: {
        name: "Packaging",
        type: AttributeType.Variant,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    benefits: {
        name: "Key Benefits",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    safetyAdvice: {
        name: "Safety Advice",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    productHighlights: {
        name: "Product Highlights",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    productSpecifications: {
        name: "Product Specifications and Features",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    manufacturerAddress: {
        name: "Manufacturer Address",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    manufacturedBy: {
        name: "Manufactured By",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    importedBy: {
        name: "Imported By / Marketed By",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    aboutBrand: {
        name: "About Brand",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    allergenInformation: {
        name: "Allergen Information",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    features: {
        name: "Features",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    goodToKnow: {
        name: "Good to know",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    idealFor: {
        name: "Ideal for",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    quickTips: {
        name: "Quick Tips",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    prescriptionRequired: {
        name: "Prescription Required",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    ifMiss: {
        name: "If Miss",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    formulation: {
        name: "Formulation",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    areaOfApplication: {
        name: "Area of application",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    compatibleWith: {
        name: "Compatible with",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    fragranceNotes: {
        name: "Fragrance Notes",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    packSize: {
        name: "Pack Size",
        type: AttributeType.Optional,
        inputType: InputFieldType.Dropdown,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
    flavour: {
        name: "Flavour / color",
        type: AttributeType.Optional,
        inputType: InputFieldType.TextBox,
        multiple: false,
        fields: [
            {
                name: "value",
                type: "text",
                multiple: false,
                required: true,
            },
        ],
    },
};
