import mongoose, { Schema, Document, Types } from 'mongoose';
import { ENUM_ROLE_TYPE } from './role.enum';

export const RoleTableName = 'roles';

export interface IRole extends Document {
  name: string;
  description?: string;
  isActive: boolean;
  isDefault?: boolean;
  type: ENUM_ROLE_TYPE;
  policies?: Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

const RoleSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      index: true,
      trim: true,
      maxlength: 30
    },
    description: {
      type: String,
      required: false,
      trim: true
    },
    isActive: {
      type: Boolean,
      required: true,
      default: true,
      index: true
    },
    isDefault: {
      type: Boolean,
      required: false,
      default: false,
      index: true
    },
    type: {
      type: String,
      required: true,
      index: true,
      unique: true,
      maxlength: 30,
      enum: Object.values(ENUM_ROLE_TYPE)
    },
    policies: {
      type: [Schema.Types.ObjectId],
      required: false,
      ref: 'Policy',
      default: []
    }
  },
  {
    timestamps: true,
    versionKey: false
  }
);


export const Role = mongoose.model<IRole>('Role', RoleSchema, RoleTableName);