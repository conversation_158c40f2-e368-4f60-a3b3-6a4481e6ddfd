import { Types } from 'mongoose';
import { User, IUser as UserDocument } from './user.model';
import { Role, IRole as RoleDocument } from '../role/role.model';
import { Staff } from './staff.model';
import { Client } from './client.model';
import LoggerConfig from '../../common/logger/log.module';
import { ENUM_ROLE_TYPE } from '../role/role.enum';
import { IFacility } from '../facility/facility.model';
import {
  ICsvUser,
  IBatchConfig,
  IUserMigrationResult,
  IBatchResult,
  IBatchContext,
  IMigrationStats,
  MigrationPhase,
  DEFAULT_BATCH_CONFIG
} from './user.migration.types';
import { getStaff, getClient } from './user.migration';

const logger = LoggerConfig('user.migration.batch');

/**
 * Batch processor for user migration
 */
export class UserMigrationBatchProcessor {
  private config: IBatchConfig;
  private stats: IMigrationStats;
  private roleMap: Map<string, Types.ObjectId>;

  constructor(config: IBatchConfig = DEFAULT_BATCH_CONFIG) {
    this.config = config;
    this.stats = this.initializeStats();
    this.roleMap = new Map();
  }

  /**
   * Initialize migration statistics
   */
  private initializeStats(): IMigrationStats {
    return {
      totalRecords: 0,
      totalBatches: 0,
      totalProcessed: 0,
      totalSuccessful: 0,
      totalFailed: 0,
      totalUsers: 0,
      totalStaff: 0,
      totalClients: 0,
      validationErrors: 0,
      duplicateErrors: 0,
      databaseErrors: 0,
      facilityErrors: 0,
      roleErrors: 0,
      unknownErrors: 0,
      startTime: new Date()
    };
  }

  /**
   * Process users in batches
   */
  async processBatches(
    userDataWithRows: Array<{ data: ICsvUser; rowNumber: number }>
  ): Promise<{ stats: IMigrationStats; results: IUserMigrationResult[] }> {
    try {
      logger.info(`Starting batch processing of ${userDataWithRows.length} users`);

      // Initialize stats
      this.stats.totalRecords = userDataWithRows.length;
      this.stats.totalBatches = Math.ceil(userDataWithRows.length / this.config.batchSize);
      this.stats.startTime = new Date();

      // Load roles once
      await this.loadRoles();

      const allResults: IUserMigrationResult[] = [];

      // Process in batches
      for (let i = 0; i < userDataWithRows.length; i += this.config.batchSize) {
        const batchNumber = Math.floor(i / this.config.batchSize) + 1;
        const startIndex = i;
        const endIndex = Math.min(i + this.config.batchSize, userDataWithRows.length);
        const batchData = userDataWithRows.slice(startIndex, endIndex);

        logger.info(`Processing batch ${batchNumber}/${this.stats.totalBatches} (records ${startIndex + 1}-${endIndex})`);

        const batchContext: IBatchContext = {
          batchNumber,
          startIndex,
          endIndex,
          batchData,
          session: null, // Will be created per batch
          stats: this.stats,
          config: this.config
        };

        const batchResult = await this.processBatchWithRetry(batchContext);
        allResults.push(...batchResult.results);

        // Update overall stats
        this.updateStats(batchResult);

        // Log progress
        if (batchNumber % this.config.logProgressInterval === 0 || batchNumber === this.stats.totalBatches) {
          this.logProgress(batchNumber);
        }
      }

      // Finalize stats
      this.stats.endTime = new Date();
      this.stats.totalProcessingTime = this.stats.endTime.getTime() - this.stats.startTime.getTime();
      this.stats.averageBatchTime = this.stats.totalProcessingTime / this.stats.totalBatches;
      this.stats.recordsPerSecond = this.stats.totalProcessed / (this.stats.totalProcessingTime / 1000);

      logger.info(`Batch processing completed. Processed ${this.stats.totalProcessed} records in ${this.stats.totalBatches} batches`);

      return {
        stats: this.stats,
        results: allResults
      };

    } catch (error) {
      logger.error('Error in batch processing:', error);
      throw error;
    }
  }

  /**
   * Process a single batch with retry mechanism
   */
  private async processBatchWithRetry(context: IBatchContext): Promise<IBatchResult> {
    let retryCount = 0;
    let lastError: any;

    while (retryCount <= this.config.maxRetries) {
      try {
        return await this.processBatch(context);
      } catch (error) {
        lastError = error;
        retryCount++;

        if (retryCount <= this.config.maxRetries) {
          logger.warn(`Batch ${context.batchNumber} failed (attempt ${retryCount}/${this.config.maxRetries + 1}), retrying in ${this.config.retryDelayMs}ms...`);
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelayMs * retryCount));
        } else {
          logger.error(`Batch ${context.batchNumber} failed after ${this.config.maxRetries + 1} attempts`);
          break;
        }
      }
    }

    // If all retries failed, return a failed batch result
    const failedResults: IUserMigrationResult[] = context.batchData.map(({ data, rowNumber }) => ({
      rowNumber,
      csvData: data,
      success: false,
      errorMessage: `Batch processing failed after ${this.config.maxRetries + 1} attempts: ${lastError?.message || 'Unknown error'}`,
      errorType: 'DATABASE',
      processingTime: 0,
      processedAt: new Date()
    }));

    return {
      batchNumber: context.batchNumber,
      batchSize: context.batchData.length,
      startTime: new Date(),
      endTime: new Date(),
      processingTime: 0,
      successCount: 0,
      errorCount: failedResults.length,
      results: failedResults
    };
  }

  /**
   * Process a single batch
   */
  private async processBatch(context: IBatchContext): Promise<IBatchResult> {
    const batchStartTime = new Date();
    const batchResults: IUserMigrationResult[] = [];
    let session: any = null;

    try {
      // Create a new session for this batch
      const mongoose = require('mongoose');
      session = await mongoose.startSession();

      // Start transaction with timeout
      session.startTransaction({
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority' },
        maxTimeMS: this.config.transactionTimeoutMs
      });

      // Track email/mobile to user mapping for parent-child relationships within this batch
      const emailMobileMap = new Map<string, { userDoc: UserDocument, rowNumber: number }>();
      const usersToInsert: UserDocument[] = [];
      const staffsToInsert: any[] = [];
      const clientsToInsert: any[] = [];

      // Process each user in the batch
      for (const { data: user, rowNumber } of context.batchData) {
        const userStartTime = Date.now();

        try {
          const result = await this.processUser(
            user,
            rowNumber,
            emailMobileMap,
            usersToInsert,
            staffsToInsert,
            clientsToInsert
          );

          result.processingTime = Date.now() - userStartTime;
          batchResults.push(result);

        } catch (error) {
          const processingTime = Date.now() - userStartTime;
          logger.error(`Error processing user at row ${rowNumber}:`, error);

          batchResults.push({
            rowNumber,
            csvData: user,
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            errorType: 'UNKNOWN',
            processingTime,
            processedAt: new Date()
          });
        }
      }

      // Bulk insert all users, staff, and clients for this batch
      await this.performBulkInserts(usersToInsert, staffsToInsert, clientsToInsert, session);

      // Commit the transaction
      await session.commitTransaction();
      logger.info(`Batch ${context.batchNumber}: Transaction committed successfully`);

      // Update success status for inserted records
      this.updateResultsWithInsertedIds(batchResults, usersToInsert, staffsToInsert, clientsToInsert);

      const batchEndTime = new Date();
      const processingTime = batchEndTime.getTime() - batchStartTime.getTime();

      return {
        batchNumber: context.batchNumber,
        batchSize: context.batchData.length,
        startTime: batchStartTime,
        endTime: batchEndTime,
        processingTime,
        successCount: batchResults.filter(r => r.success).length,
        errorCount: batchResults.filter(r => !r.success).length,
        results: batchResults
      };

    } catch (error) {
      logger.error(`Error processing batch ${context.batchNumber}:`, error);

      // Abort transaction if it exists
      if (session) {
        try {
          await session.abortTransaction();
          logger.info(`Batch ${context.batchNumber}: Transaction aborted due to error`);
        } catch (abortError) {
          logger.error(`Error aborting transaction for batch ${context.batchNumber}:`, abortError);
        }
      }

      // Check if this is a transient error that should be retried
      if (this.isTransientError(error)) {
        logger.warn(`Batch ${context.batchNumber}: Transient error detected, will retry`);
        throw error; // Let the retry mechanism handle it
      }

      // Mark all records in this batch as failed
      const failedResults: IUserMigrationResult[] = context.batchData.map(({ data, rowNumber }) => ({
        rowNumber,
        csvData: data,
        success: false,
        errorMessage: `Batch processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        errorType: 'DATABASE',
        processingTime: 0,
        processedAt: new Date()
      }));

      const batchEndTime = new Date();
      const processingTime = batchEndTime.getTime() - batchStartTime.getTime();

      return {
        batchNumber: context.batchNumber,
        batchSize: context.batchData.length,
        startTime: batchStartTime,
        endTime: batchEndTime,
        processingTime,
        successCount: 0,
        errorCount: failedResults.length,
        results: failedResults
      };
    } finally {
      // End session
      if (session) {
        try {
          await session.endSession();
        } catch (endError) {
          logger.error(`Error ending session for batch ${context.batchNumber}:`, endError);
        }
      }
    }
  }

  /**
   * Process a single user
   */
  private async processUser(
    user: ICsvUser,
    rowNumber: number,
    emailMobileMap: Map<string, { userDoc: UserDocument, rowNumber: number }>,
    usersToInsert: UserDocument[],
    staffsToInsert: any[],
    clientsToInsert: any[]
  ): Promise<IUserMigrationResult> {

    // Validation
    const validationError = this.validateUser(user, rowNumber);
    if (validationError) {
      return {
        rowNumber,
        csvData: user,
        success: false,
        errorMessage: validationError,
        errorType: 'VALIDATION',
        processingTime: 0,
        processedAt: new Date()
      };
    }

    // Create user document
    const userDoc = this.createUserDocument(user, emailMobileMap, rowNumber);
    usersToInsert.push(userDoc);

    // Create staff or client document
    const facility = this.getFacilityForUser(user);
    if (!facility) {
      return {
        rowNumber,
        csvData: user,
        success: false,
        errorMessage: `Facility not found for user ${user.firstName} ${user.lastName}`,
        errorType: 'FACILITY',
        processingTime: 0,
        processedAt: new Date()
      };
    }

    const staffRoles = [
      ENUM_ROLE_TYPE.WEB_MASTER,
      ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
      ENUM_ROLE_TYPE.TRAINER
    ];

    if (staffRoles.includes(user.role as ENUM_ROLE_TYPE)) {
      const staff = await getStaff(userDoc, user, global.config.organizationId, facility._id.toString());
      staffsToInsert.push(staff);
    } else if (user.role === ENUM_ROLE_TYPE.USER) {
      const client = await getClient(userDoc, user, global.config.organizationId, facility._id);
      clientsToInsert.push(client);
    }

    return {
      rowNumber,
      csvData: user,
      success: true,
      processingTime: 0,
      processedAt: new Date()
    };
  }

  /**
   * Load roles into memory
   */
  private async loadRoles(): Promise<void> {
    try {
      const roles = await Role.find().exec();
      this.roleMap.clear();

      roles.forEach((role: RoleDocument) => {
        this.roleMap.set(role.type, role._id as Types.ObjectId);
      });

      logger.info(`Loaded ${roles.length} roles into memory`);
    } catch (error) {
      logger.error('Error loading roles:', error);
      throw error;
    }
  }

  /**
   * Validate user data
   */
  private validateUser(user: ICsvUser, rowNumber: number): string | null {
    if (!user.firstName) {
      return `Row ${rowNumber}: User with id ${user.id} has no first name`;
    }

    if (!user.email && !user.mobile) {
      return `Row ${rowNumber}: User with id ${user.id} has no email or mobile`;
    }

    if (!user.role) {
      return `Row ${rowNumber}: User with id ${user.id} has no role`;
    }

    return null;
  }

  /**
   * Create user document
   */
  private createUserDocument(
    user: ICsvUser,
    emailMobileMap: Map<string, { userDoc: UserDocument, rowNumber: number }>,
    rowNumber: number
  ): UserDocument {
    const userDoc = new User({
      organizationId: new Types.ObjectId(global.config.organizationId),
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      firstName: user.firstName,
      lastName: user.lastName,
      countryCode: user.countryCode,
      mobile: user.mobile,
      email: user.email,
      isActive: user.isActive || false,
      newUser: true,
      role: this.roleMap.get(user.role) || this.roleMap.get(ENUM_ROLE_TYPE.USER),
      assignedPolicies: [],
      restrictedPolicies: [],
      gender: user.gender,
      parent: null,
      createdAt: user.createdAt ? new Date(user.createdAt) : new Date()
    });

    // Handle parent-child relationships
    const identifiers = [];
    if (user.email) identifiers.push({ type: 'email', value: user.email });
    if (user.mobile) identifiers.push({ type: 'mobile', value: user.mobile });

    let foundParent = false;
    for (const { type, value } of identifiers) {
      const existingUser = emailMobileMap.get(value);
      if (existingUser) {
        userDoc.parent = existingUser.userDoc._id as Types.ObjectId;
        logger.info(`Row ${rowNumber}: Setting user ${user.firstName} ${user.lastName} as child of user from row ${existingUser.rowNumber} (same ${type}: ${value})`);
        foundParent = true;
        break;
      }
    }

    if (!foundParent) {
      for (const { value } of identifiers) {
        if (!emailMobileMap.has(value)) {
          emailMobileMap.set(value, { userDoc, rowNumber });
        }
      }
    }

    return userDoc;
  }

  /**
   * Get facility for user
   */
  private getFacilityForUser(user: ICsvUser): IFacility | null {
    return global.facilityMap.get(user.facilityId) ??
      global.facilityMap.values().next()?.value ??
      null;
  }

  /**
   * Perform bulk inserts
   */
  private async performBulkInserts(
    users: UserDocument[],
    staffs: any[],
    clients: any[],
    session: any
  ): Promise<void> {
    try {
      if (users.length > 0) {
        await User.insertMany(users, { session });
        logger.info(`Inserted ${users.length} users`);
      }

      if (staffs.length > 0) {
        await Staff.insertMany(staffs, { session });
        logger.info(`Inserted ${staffs.length} staff records`);
      }

      if (clients.length > 0) {
        await Client.insertMany(clients, { session });
        logger.info(`Inserted ${clients.length} client records`);
      }
    } catch (error) {
      logger.error('Error in bulk inserts:', error);
      throw error;
    }
  }

  /**
   * Update results with inserted IDs
   */
  private updateResultsWithInsertedIds(
    results: IUserMigrationResult[],
    users: UserDocument[],
    staffs: any[],
    clients: any[]
  ): void {
    // Update user IDs
    results.forEach((result, index) => {
      if (result.success && users[index]) {
        result.userId = new Types.ObjectId(users[index]._id as any);
      }
    });

    // Update staff IDs
    let staffIndex = 0;
    results.forEach(result => {
      if (result.success && result.csvData.role !== ENUM_ROLE_TYPE.USER && staffs[staffIndex]) {
        result.staffId = staffs[staffIndex]._id;
        staffIndex++;
      }
    });

    // Update client IDs
    let clientIndex = 0;
    results.forEach(result => {
      if (result.success && result.csvData.role === ENUM_ROLE_TYPE.USER && clients[clientIndex]) {
        result.clientId = clients[clientIndex]._id;
        clientIndex++;
      }
    });
  }

  /**
   * Update overall statistics
   */
  private updateStats(batchResult: IBatchResult): void {
    this.stats.totalProcessed += batchResult.batchSize;
    this.stats.totalSuccessful += batchResult.successCount;
    this.stats.totalFailed += batchResult.errorCount;

    // Count by type
    batchResult.results.forEach(result => {
      if (result.success) {
        this.stats.totalUsers++;
        if (result.staffId) this.stats.totalStaff++;
        if (result.clientId) this.stats.totalClients++;
      } else {
        switch (result.errorType) {
          case 'VALIDATION':
            this.stats.validationErrors++;
            break;
          case 'DUPLICATE':
            this.stats.duplicateErrors++;
            break;
          case 'DATABASE':
            this.stats.databaseErrors++;
            break;
          case 'FACILITY':
            this.stats.facilityErrors++;
            break;
          case 'ROLE':
            this.stats.roleErrors++;
            break;
          default:
            this.stats.unknownErrors++;
        }
      }
    });
  }

  /**
   * Log progress
   */
  private logProgress(currentBatch: number): void {
    const percentComplete = (currentBatch / this.stats.totalBatches) * 100;
    const elapsed = Date.now() - this.stats.startTime.getTime();
    const estimatedTotal = (elapsed / currentBatch) * this.stats.totalBatches;
    const remaining = estimatedTotal - elapsed;

    logger.info(`Progress: ${currentBatch}/${this.stats.totalBatches} batches (${percentComplete.toFixed(1)}%)`);
    logger.info(`Processed: ${this.stats.totalProcessed}/${this.stats.totalRecords} records`);
    logger.info(`Success: ${this.stats.totalSuccessful}, Failed: ${this.stats.totalFailed}`);
    logger.info(`Estimated time remaining: ${Math.round(remaining / 1000)}s`);
  }

  /**
   * Check if an error is transient and should be retried
   */
  private isTransientError(error: any): boolean {
    if (!error) return false;

    // MongoDB transient transaction errors
    if (error.errorLabels && error.errorLabels.includes('TransientTransactionError')) {
      return true;
    }

    // Network errors
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
      return true;
    }

    // MongoDB server selection errors
    if (error.name === 'MongoServerSelectionError' || error.name === 'MongoNetworkError') {
      return true;
    }

    // Transaction timeout errors
    if (error.code === 251 && error.codeName === 'NoSuchTransaction') {
      return true;
    }

    return false;
  }

  /**
   * Get migration statistics
   */
  getStats(): IMigrationStats {
    return { ...this.stats };
  }
}
