import * as path from 'path';
import { CsvWriter } from '../../common/utils/csv-writer';
import LoggerConfig from '../../common/logger/log.module';
import {
  IUserMigrationResult,
  IMigrationStats,
  ISuccessfulMigrationRecord,
  IFailedMigrationRecord,
  SUCCESSFUL_MIGRATION_HEADERS,
  FAILED_MIGRATION_HEADERS
} from './user.migration.types';

const logger = LoggerConfig('user.migration.results');

/**
 * Result generator for user migration
 */
export class MigrationResultGenerator {

  /**
   * Generate CSV files for migration results
   */
  static async generateResultFiles(
    results: IUserMigrationResult[],
    stats: IMigrationStats
  ): Promise<{ successful: string; failed: string; summary: string }> {
    try {
      logger.info('Generating migration result CSV files...');

      // Ensure processed directory exists
      const processedDir = CsvWriter.ensureProcessedDirectory('migration-results');

      // Create timestamped filenames
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const successfulFile = path.join(processedDir, `successful-users-${timestamp}.csv`);
      const failedFile = path.join(processedDir, `failed-users-${timestamp}.csv`);
      const summaryFile = path.join(processedDir, `migration-summary-${timestamp}.csv`);

      // Separate successful and failed results
      const successfulResults = results.filter(r => r.success);
      const failedResults = results.filter(r => !r.success);

      // Generate successful records CSV
      if (successfulResults.length > 0) {
        const successfulRecords = this.transformToSuccessfulRecords(successfulResults);
        await CsvWriter.writeToFile(successfulRecords, successfulFile, {
          headers: SUCCESSFUL_MIGRATION_HEADERS
        });
        logger.info(`Generated successful records CSV: ${successfulFile} (${successfulResults.length} records)`);
      } else {
        logger.info('No successful records to write');
      }

      // Generate failed records CSV
      if (failedResults.length > 0) {
        const failedRecords = this.transformToFailedRecords(failedResults);
        await CsvWriter.writeToFile(failedRecords, failedFile, {
          headers: FAILED_MIGRATION_HEADERS
        });
        logger.info(`Generated failed records CSV: ${failedFile} (${failedResults.length} records)`);
      } else {
        logger.info('No failed records to write');
      }

      // Generate summary CSV
      const summaryRecords = this.generateSummaryRecords(stats);
      await CsvWriter.writeToFile(summaryRecords, summaryFile);
      logger.info(`Generated migration summary CSV: ${summaryFile}`);

      return {
        successful: successfulFile,
        failed: failedFile,
        summary: summaryFile
      };

    } catch (error) {
      logger.error('Error generating result files:', error);
      throw error;
    }
  }

  /**
   * Transform migration results to successful records format
   */
  private static transformToSuccessfulRecords(results: IUserMigrationResult[]): ISuccessfulMigrationRecord[] {
    return results.map(result => ({
      rowNumber: result.rowNumber,
      id: result.csvData.id,
      firstName: result.csvData.firstName,
      lastName: result.csvData.lastName,
      email: result.csvData.email || '',
      mobile: result.csvData.mobile || '',
      role: result.csvData.role,
      facilityId: result.csvData.facilityId || '',
      userId: result.userId?.toString() || '',
      staffId: result.staffId?.toString() || '',
      clientId: result.clientId?.toString() || '',
      processingTime: result.processingTime || 0,
      processedAt: result.processedAt.toISOString(),
      batchNumber: this.getBatchNumberFromRowNumber(result.rowNumber, 100) // Assuming default batch size
    }));
  }

  /**
   * Transform migration results to failed records format
   */
  private static transformToFailedRecords(results: IUserMigrationResult[]): IFailedMigrationRecord[] {
    return results.map(result => ({
      rowNumber: result.rowNumber,
      id: result.csvData.id,
      firstName: result.csvData.firstName,
      lastName: result.csvData.lastName,
      email: result.csvData.email || '',
      mobile: result.csvData.mobile || '',
      role: result.csvData.role,
      facilityId: result.csvData.facilityId || '',
      errorType: result.errorType || 'UNKNOWN',
      errorMessage: result.errorMessage || 'Unknown error',
      processingTime: result.processingTime || 0,
      processedAt: result.processedAt.toISOString(),
      batchNumber: this.getBatchNumberFromRowNumber(result.rowNumber, 100) // Assuming default batch size
    }));
  }

  /**
   * Generate summary records for CSV
   */
  private static generateSummaryRecords(stats: IMigrationStats): Array<Record<string, any>> {
    const totalTime = stats.endTime && stats.startTime ?
      stats.endTime.getTime() - stats.startTime.getTime() : 0;

    return [
      {
        metric: 'Total Records',
        value: stats.totalRecords,
        percentage: '100.00%',
        description: 'Total number of records in CSV file'
      },
      {
        metric: 'Total Processed',
        value: stats.totalProcessed,
        percentage: `${((stats.totalProcessed / Math.max(stats.totalRecords, 1)) * 100).toFixed(2)}%`,
        description: 'Total number of records processed'
      },
      {
        metric: 'Total Successful',
        value: stats.totalSuccessful,
        percentage: `${((stats.totalSuccessful / Math.max(stats.totalRecords, 1)) * 100).toFixed(2)}%`,
        description: 'Total number of successfully migrated records'
      },
      {
        metric: 'Total Failed',
        value: stats.totalFailed,
        percentage: `${((stats.totalFailed / Math.max(stats.totalRecords, 1)) * 100).toFixed(2)}%`,
        description: 'Total number of failed records'
      },
      {
        metric: 'Users Created',
        value: stats.totalUsers,
        percentage: `${((stats.totalUsers / Math.max(stats.totalSuccessful, 1)) * 100).toFixed(2)}%`,
        description: 'Total number of user records created'
      },
      {
        metric: 'Staff Created',
        value: stats.totalStaff,
        percentage: `${((stats.totalStaff / Math.max(stats.totalSuccessful, 1)) * 100).toFixed(2)}%`,
        description: 'Total number of staff records created'
      },
      {
        metric: 'Clients Created',
        value: stats.totalClients,
        percentage: `${((stats.totalClients / Math.max(stats.totalSuccessful, 1)) * 100).toFixed(2)}%`,
        description: 'Total number of client records created'
      },
      {
        metric: 'Validation Errors',
        value: stats.validationErrors,
        percentage: `${((stats.validationErrors / Math.max(stats.totalFailed, 1)) * 100).toFixed(2)}%`,
        description: 'Records failed due to validation errors'
      },
      {
        metric: 'Duplicate Errors',
        value: stats.duplicateErrors,
        percentage: `${((stats.duplicateErrors / Math.max(stats.totalFailed, 1)) * 100).toFixed(2)}%`,
        description: 'Records failed due to duplicate data'
      },
      {
        metric: 'Database Errors',
        value: stats.databaseErrors,
        percentage: `${((stats.databaseErrors / Math.max(stats.totalFailed, 1)) * 100).toFixed(2)}%`,
        description: 'Records failed due to database errors'
      },
      {
        metric: 'Facility Errors',
        value: stats.facilityErrors,
        percentage: `${((stats.facilityErrors / Math.max(stats.totalFailed, 1)) * 100).toFixed(2)}%`,
        description: 'Records failed due to facility not found'
      },
      {
        metric: 'Role Errors',
        value: stats.roleErrors,
        percentage: `${((stats.roleErrors / Math.max(stats.totalFailed, 1)) * 100).toFixed(2)}%`,
        description: 'Records failed due to role errors'
      },
      {
        metric: 'Unknown Errors',
        value: stats.unknownErrors,
        percentage: `${((stats.unknownErrors / Math.max(stats.totalFailed, 1)) * 100).toFixed(2)}%`,
        description: 'Records failed due to unknown errors'
      },
      {
        metric: 'Total Batches',
        value: stats.totalBatches,
        percentage: '100.00%',
        description: 'Total number of processing batches'
      },
      {
        metric: 'Processing Time (ms)',
        value: totalTime,
        percentage: '100.00%',
        description: 'Total processing time in milliseconds'
      },
      {
        metric: 'Average Batch Time (ms)',
        value: Math.round(stats.averageBatchTime || 0),
        percentage: `${((stats.averageBatchTime || 0) / Math.max(totalTime, 1) * 100).toFixed(2)}%`,
        description: 'Average time per batch in milliseconds'
      },
      {
        metric: 'Records Per Second',
        value: (stats.recordsPerSecond || 0).toFixed(2),
        percentage: '100.00%',
        description: 'Average processing speed in records per second'
      },
      {
        metric: 'Success Rate',
        value: `${((stats.totalSuccessful / Math.max(stats.totalProcessed, 1)) * 100).toFixed(2)}%`,
        percentage: '100.00%',
        description: 'Overall success rate of the migration'
      },
      {
        metric: 'Start Time',
        value: stats.startTime.toISOString(),
        percentage: '0.00%',
        description: 'Migration start timestamp'
      },
      {
        metric: 'End Time',
        value: stats.endTime?.toISOString() || 'N/A',
        percentage: '100.00%',
        description: 'Migration end timestamp'
      }
    ];
  }

  /**
   * Get batch number from row number
   */
  private static getBatchNumberFromRowNumber(rowNumber: number, batchSize: number): number {
    return Math.ceil((rowNumber - 1) / batchSize);
  }

  /**
   * Generate detailed error report
   */
  static async generateErrorReport(
    results: IUserMigrationResult[],
    outputPath?: string
  ): Promise<string> {
    try {
      const failedResults = results.filter(r => !r.success);

      if (failedResults.length === 0) {
        logger.info('No errors to report');
        return '';
      }

      const processedDir = CsvWriter.ensureProcessedDirectory('migration-results');
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const errorReportFile = outputPath || path.join(processedDir, `error-report-${timestamp}.csv`);

      // Group errors by type
      const errorsByType = failedResults.reduce((acc, result) => {
        const errorType = result.errorType || 'UNKNOWN';
        if (!acc[errorType]) {
          acc[errorType] = [];
        }
        acc[errorType].push(result);
        return acc;
      }, {} as Record<string, IUserMigrationResult[]>);

      // Create detailed error report
      const errorReport = [];

      for (const [errorType, errors] of Object.entries(errorsByType)) {
        errorReport.push({
          errorType,
          count: errors.length,
          percentage: `${((errors.length / failedResults.length) * 100).toFixed(2)}%`,
          sampleRowNumbers: errors.slice(0, 5).map(e => e.rowNumber).join(', '),
          sampleErrorMessages: errors.slice(0, 3).map(e => e.errorMessage).join(' | ')
        });
      }

      await CsvWriter.writeToFile(errorReport, errorReportFile);
      logger.info(`Generated error report: ${errorReportFile}`);

      return errorReportFile;

    } catch (error) {
      logger.error('Error generating error report:', error);
      throw error;
    }
  }
}
