import { IUser } from 'src/modules/user/user.model';
import { config } from '../common/config/config';
import { IFacility } from '../modules/facility/facility.model';


declare global {
    var config: typeof config;
    var organizationId: string;
    var organization: IUser;
    var organizationSetting: string;
    var roleMap: Map<string, any>;
    var cityMap: Map<string, any>;
    var stateMap: Map<string, any>;
    var facilityMap: Map<string, IFacility>;
    var facilitiesMap: Map<string, any>;
    var facilitiesIdMap: Map<string, string>;

}


