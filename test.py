

from __future__ import annotations
import os
import csv
import argparse
from typing import List, Dict
from datetime import datetime
import pymysql
import re
import PyPDF2
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import pymongo
import os
import dotenv
from bson import ObjectId

dotenv.load_dotenv()



client = mongo_client = pymongo.MongoClient(os.getenv("MONGODB_URI"))
database = client["hopwellness-production"]


user_collection = database["users"]
client_collection = database["clients"]
organizationId = ObjectId(os.getenv('ORGANIZATION_ID', '68bace3243579a902d78624e'))
user2024 = list(client_collection.aggregate([
    {
        '$match': {
            'organizationId': organizationId
        }
    }, {
        '$set': {
            'year': {
                '$year': '$createdAt'
            }
        }
    }, {
        '$match': {
            'year': 2025
        }
    }, 
    {
        '$lookup': {
            'from': 'users', 
            'localField': 'userId', 
            'foreignField': '_id', 
            'as': 'user', 
            'pipeline': [
                {
                    '$match': {
                        'id': {
                            '$exists': True
                        }
                    }
                }
            ]
        }
    }, {
        '$unwind': {
            'path': '$user', 
            'preserveNullAndEmptyArrays': False
        }
    },
    {
        '$replaceRoot': {
            'newRoot': '$user'
        }
    }
]))


dockumentLocker = database["documentlockers"]

documents = list(dockumentLocker.find({
    'userId':{'$in': [ u['_id'] for u in user2024]}
}))


userMap = {str(u['_id']): u for u in user2024}
inDocSet = set()

for doc in documents:
    user = userMap.get(str(doc['userId']))
    inDocSet.add(str(user['_id']))
    
    if(user['parent']):
        inDocSet.add(str(user['parent']))
        
        
useridNotInDoc = [u for u in user2024 if str(u['_id']) not in inDocSet]

# notInDocUser = user_collection.find({
#     'id': { '$in': [u['_id'] for u in useridNotInDoc]}
# })


with open('./dont-have-waiver.csv', 'w', newline='', encoding='utf-8') as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(['id', 'first_name', 'last_name', 'email', 'phone'])
    for user in useridNotInDoc:
        writer.writerow([str(user['_id']), user['firstName'], user['lastName'], user.get('email', ""), user.get('mobile', "")])


