#!/usr/bin/env python3
"""
scan_and_lookup_backwards.py

Scan directory recursively, parse filenames from the BACK, and produce CSV:
file_id,db_id,first_name,middle_name,last_name,dob,path

Filename parsing rules (scanning from back):
- tokens = filename-without-ext split by '-'
- last token -> file_id
- date: either a single token matching MM-DD-YYYY (or variants) OR three numeric tokens (MM, DD, YYYY)
  We detect date by scanning backwards from the token before file_id.
- name tokens = tokens before the date (in file order). Interpret:
  - 1 token  => last = token0
  - 2 tokens => last = token0, first = token1
  - 3+ tokens => last = token0, first = token1, middle = " ".join(token2:)
"""
from __future__ import annotations
import os
import csv
import argparse
from typing import List, Dict
from datetime import datetime
import pymysql
import re
import PyPDF2
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# ---------- Config ----------
CSV_HEADER = ["file_id", "db_id", "first_name", "middle_name", "last_name", "dob", "email", "phone", "waiver_date", "path"]

BATCH_IN_SIZE = 500

DB_TABLE = "customers"
DB_FULLNAME_EXPR = "CONCAT_WS(' ', FIRSTNAME, LASTNAME)"  # keep as your DB expects
DB_BDAY_COL = "BDAY"
DB_ID_COL = "CUSTOMER_ID"
DB_EMAIL_COL = "EMAIL"  # Add email column name
DB_PHONE_COL = "CELL_PHONE"  # Change to actual phone column name
DB_WAIVER_DATE_COL = "FACILITY_WAIVER_DATE"  # Waiver date column

# ---------- Helpers ----------
_date_token_re = re.compile(r'^\d{1,4}([\-_./]\d{1,4}){1,2}$')  # matches tokens like 07-07-2002 or 2002/07/07

def try_parse_date_from_string(token: str) -> str:
    """
    Try parse a single token date like '07-07-2002' or '2002_07_07'.
    Returns YYYY-MM-DD or empty string.
    """
    token = token.strip()
    formats = ["%m-%d-%Y", "%d-%m-%Y", "%Y-%m-%d", "%m_%d_%Y", "%d_%m_%Y", "%Y_%m_%d",
               "%m.%d.%Y", "%d.%m.%Y", "%Y.%m.%d", "%m/%d/%Y", "%d/%m/%Y", "%Y/%m/%d"]
    for fmt in formats:
        try:
            dt = datetime.strptime(token, fmt)
            return dt.strftime("%Y-%m-%d")
        except Exception:
            pass
    digits = "".join(ch for ch in token if ch.isdigit())
    if len(digits) == 8:
        for fmt in ("%m%d%Y", "%d%m%Y", "%Y%m%d"):
            try:
                dt = datetime.strptime(digits, fmt)
                return dt.strftime("%Y-%m-%d")
            except Exception:
                pass
    return ""

def convert_date_format(date_str: str) -> str:
    """Convert date from DD-MM-YYYY to YYYY-MM-DD format."""
    if not date_str or date_str.strip() == "":
        return ""
    
    try:
        # Try parsing DD-MM-YYYY format
        dt = datetime.strptime(date_str.strip(), "%d-%m-%Y")
        return dt.strftime("%Y-%m-%d")
    except ValueError:
        try:
            # Try parsing MM-DD-YYYY format
            dt = datetime.strptime(date_str.strip(), "%m-%d-%Y")
            return dt.strftime("%Y-%m-%d")
        except ValueError:
            # If already in YYYY-MM-DD format, return as is
            if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str.strip()):
                return date_str.strip()
            return ""

def parse_filename_backwards(filename: str) -> Dict[str, str]:
    """
    Parse filename (basename) scanning from the back.
    Returns dict with keys: file_id, first_name, middle_name, last_name, dob
    If parsing fails some fields may be empty strings.
    """
    base = os.path.basename(filename)
    name_only, _ext = os.path.splitext(base)
    if not name_only:
        return {"file_id": "", "first_name": "", "middle_name": "", "last_name": "", "dob": ""}

    parts = [p for p in name_only.split("-") if p != ""]
    if len(parts) < 3:
        if len(parts) == 2:
            return {"file_id": parts[-1].strip(), "first_name": "", "middle_name": "", "last_name": parts[0].strip(), "dob": ""}
        return {"file_id": "", "first_name": "", "middle_name": "", "last_name": "", "dob": ""}

    # last token is file id
    file_id = parts[-1].strip()

    # find date tokens scanning backwards
    dob = ""
    date_start_idx = None

    # try single-token date near the end
    for i in range(len(parts) - 2, max(-1, len(parts) - 6), -1):
        tok = parts[i].strip()
        if _date_token_re.match(tok):
            parsed = try_parse_date_from_string(tok)
            if parsed:
                dob = parsed
                date_start_idx = i
                break

    if date_start_idx is None:
        # try three numeric tokens immediately before file_id
        if len(parts) >= 4:
            i3 = len(parts) - 4
            if i3 >= 0:
                a, b, c = parts[i3], parts[i3 + 1], parts[i3 + 2]
                if all(re.fullmatch(r'\d{1,4}', x.strip()) for x in (a, b, c)):
                    candidate = f"{a.strip()}-{b.strip()}-{c.strip()}"
                    parsed = try_parse_date_from_string(candidate)
                    if parsed:
                        dob = parsed
                        date_start_idx = i3
                    else:
                        candidate2 = f"{c.strip()}-{b.strip()}-{a.strip()}"
                        parsed2 = try_parse_date_from_string(candidate2)
                        if parsed2:
                            dob = parsed2
                            date_start_idx = i3

    if date_start_idx is None:
        # try other nearby triples (prefer near end)
        n = len(parts)
        found = False
        for start in range(max(0, n - 6), n - 2):  # small window near the end
            if start + 3 <= n - 1:
                a, b, c = parts[start], parts[start + 1], parts[start + 2]
                if all(re.fullmatch(r'\d{1,4}', x.strip()) for x in (a, b, c)):
                    candidate = f"{a.strip()}-{b.strip()}-{c.strip()}"
                    parsed = try_parse_date_from_string(candidate)
                    if parsed:
                        dob = parsed
                        date_start_idx = start
                        found = True
                        break
        if not found and date_start_idx is None:
            date_start_idx = len(parts) - 1
            dob = ""

    # Name tokens are parts[0:date_start_idx]
    name_tokens = parts[0:date_start_idx]

    # Interpret name tokens per your rule:
    last_name = ""
    first_name = ""
    middle_name = ""
    if not name_tokens:
        pass
    elif len(name_tokens) == 1:
        last_name = name_tokens[0].strip()
    elif len(name_tokens) == 2:
        last_name = name_tokens[0].strip()
        first_name = name_tokens[1].strip()
    else:
        last_name = name_tokens[0].strip()
        first_name = name_tokens[1].strip()
        middle_name = " ".join(tok.strip() for tok in name_tokens[2:] if tok.strip())

    return {
        "file_id": file_id,
        "first_name": first_name,
        "middle_name": middle_name,
        "last_name": last_name,
        "dob": dob,
    }

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extract text from PDF file."""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
            return text
    except Exception as e:
        print(f"[WARN] Failed to extract text from {pdf_path}: {e}")
        return ""

def extract_email_from_text(text: str) -> str:
    """
    Extract email address from text.
    Handles cases where 'Email:' is followed by an address 
    that may contain spaces inside.
    """
    # Match email after "Email:" and allow spaces inside the local/domain part
    email_pattern = r'Email:\s*([A-Za-z0-9._%+\s-]+@[A-Za-z0-9.\s-]+\.[A-Za-z]{2,})'
    match = re.search(email_pattern, text, flags=re.IGNORECASE)
    if match:
        raw_email = match.group(1)
        return raw_email.replace(" ", "")  # remove unwanted spaces
    return ""

def extract_phone_from_text(text: str) -> str:
    """Extract phone number from text."""
    phone_patterns = [
        r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # ************ or ************
        r'\(\d{3}\)\s*\d{3}[-.]?\d{4}',    # (*************
        r'\b\d{10}\b',                     # 1234567890
        r'\+\d{1,3}[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}',  # ******-456-7890
    ]
    
    for pattern in phone_patterns:
        matches = re.findall(pattern, text)
        if matches:
            return matches[0]
    return ""

# ---------- Scanning ----------
def process_single_file(file_info):
    """Process a single file and return the row data."""
    dirpath, fname = file_info
    abs_path = os.path.abspath(os.path.join(dirpath, fname))
    
    parsed = parse_filename_backwards(fname)
    
    # Extract email and phone from PDF if it's a PDF file
    email = ""
    phone = ""
    if fname.lower().endswith('.pdf'):
        pdf_text = extract_text_from_pdf(abs_path)
        email = extract_email_from_text(pdf_text)
        phone = extract_phone_from_text(pdf_text)
    
    row = {
        "file_id": parsed.get("file_id", "") or "",
        "db_id": "",
        "first_name": parsed.get("first_name", "") or "",
        "middle_name": parsed.get("middle_name", "") or "",
        "last_name": parsed.get("last_name", "") or "",
        "dob": convert_date_format( parsed.get("dob", "") or ""),
        "email": email,
        "phone": phone,
        "waiver_date": "",  # Will be populated from database
        "path": abs_path,
    }
    return row

def scan_directory(root_dir: str, max_workers: int = 10) -> List[Dict[str, str]]:
    if not os.path.exists(root_dir):
        raise FileNotFoundError(f"Input directory not found: {root_dir!r}")
    
    # Collect all files first
    file_list = []
    for dirpath, _dirs, files in os.walk(root_dir):
        for fname in files:
            file_list.append((dirpath, fname))
    
    total_files = len(file_list)
    print(f"[INFO] Found {total_files} files to process with {max_workers} threads")
    
    rows = []
    lock = threading.Lock()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_file = {executor.submit(process_single_file, file_info): file_info for file_info in file_list}
        
        # Process completed tasks with progress bar
        with tqdm(total=total_files, desc="Scanning files", unit="file") as pbar:
            for future in as_completed(future_to_file):
                try:
                    row = future.result()
                    with lock:
                        rows.append(row)
                    pbar.update(1)
                except Exception as e:
                    file_info = future_to_file[future]
                    print(f"[ERROR] Failed to process {file_info[1]}: {e}")
                    pbar.update(1)
    
    return rows

# ---------- DB matching helpers (deterministic row-only variants) ----------
def generate_name_variants_from_parts(first: str, middle: str, last: str) -> List[str]:
    """
    Produce a small, deterministic set of name variants derived only from this row's parts.
    This prevents cross-row mixing and avoids producing excessive permutations.
    Variants included (if parts present):
      - full: "first middle last" (middle optional)
      - "first last"
      - "last first"
      - "last first middle" (if middle exists)
      - "first middle" (if last missing but middle exists)
    """
    variants = []
    # Normalize inputs
    first = (first or "").strip()
    middle = (middle or "").strip()
    last = (last or "").strip()

    # full (first [middle] last)
    if first and last:
        if middle:
            variants.append(f"{first} {middle} {last}")
        else:
            variants.append(f"{first} {last}")
    elif first and not last:
        if middle:
            variants.append(f"{first} {middle}")
        else:
            variants.append(first)
    elif last and not first:
        variants.append(last)

    # first last and last first (if both present)
    if first and last:
        variants.append(f"{first} {last}")
        variants.append(f"{last} {first}")
        if middle:
            variants.append(f"{last} {first} {middle}")

    # Deduplicate while preserving order
    seen = set()
    out = []
    for v in variants:
        norm = " ".join(v.split()).strip()
        if not norm:
            continue
        if norm.lower() in seen:
            continue
        seen.add(norm.lower())
        out.append(norm)
    return out

def chunked(iterable, size):
    it = iter(iterable)
    while True:
        chunk = []
        try:
            for _ in range(size):
                chunk.append(next(it))
        except StopIteration:
            if chunk:
                yield chunk
            break
        yield chunk

def build_variant_maps(rows):
    idx_to_variants = {}
    idx_to_row = {}
    for i, r in enumerate(rows):
        first = r.get("first_name", "").strip()
        middle = r.get("middle_name", "").strip()
        last = r.get("last_name", "").strip()
        dob = r.get("dob", "").strip()
        variants = generate_name_variants_from_parts(first, middle, last)
        idx_to_variants[i] = variants
        idx_to_row[i] = (first, middle, last, dob)
    return idx_to_variants, idx_to_row

def query_db_and_fill(rows, conn_params: dict, batch_size: int = BATCH_IN_SIZE):
    idx_to_variants, idx_to_row = build_variant_maps(rows)
    dob_to_variants = {}
    idx_for_variant = {}
    email_to_indices = {}
    phone_to_indices = {}
    
    for idx, variants in idx_to_variants.items():
        first, middle, last, dob = idx_to_row[idx]
        email = rows[idx].get("email", "").strip()
        phone = rows[idx].get("phone", "").strip()
        
        # Group by DOB and variants (existing logic)
        dob_to_variants.setdefault(dob, set())
        for v in variants:
            dob_to_variants[dob].add(v)
            idx_for_variant.setdefault((v, dob), []).append(idx)
        
        # Group by email and phone for matching
        if email:
            email_to_indices.setdefault(email.lower(), []).append(idx)
        if phone:
            phone_to_indices.setdefault(phone, []).append(idx)

    conn = pymysql.connect(**conn_params, cursorclass=pymysql.cursors.DictCursor, autocommit=True)
    try:
        with conn.cursor() as cur:
            # Debug: Check table structure
            cur.execute(f"DESCRIBE {DB_TABLE}")
            columns = cur.fetchall()
            print(f"[DEBUG] Available columns in {DB_TABLE}:")
            for col in columns:
                print(f"  - {col['Field']} ({col['Type']})")
            
            for dob, variants_set in dob_to_variants.items():
                if not dob:
                    continue
                variants_list = list(variants_set)
                
                # Get emails and phones for rows with this DOB
                dob_emails = []
                dob_phones = []
                for idx, (f, m, l, d) in idx_to_row.items():
                    if d == dob:
                        email = rows[idx].get("email", "").strip()
                        phone = rows[idx].get("phone", "").strip()
                        if email:
                            dob_emails.append(email.lower())
                        if phone:
                            dob_phones.append(phone)
                
                for chunk in chunked(variants_list, batch_size):
                    placeholders = ", ".join(["%s"] * len(chunk))
                    
                    # Build WHERE conditions
                    where_parts = [f"{DB_FULLNAME_EXPR} IN ({placeholders})"]
                    params = [dob] + chunk
                    
                    if dob_emails:
                        email_placeholders = ", ".join(["%s"] * len(dob_emails))
                        where_parts.append(f"LOWER({DB_EMAIL_COL}) IN ({email_placeholders})")
                        params.extend(dob_emails)
                    
                    if dob_phones:
                        phone_placeholders = ", ".join(["%s"] * len(dob_phones))
                        where_parts.append(f"{DB_PHONE_COL} IN ({phone_placeholders})")
                        params.extend(dob_phones)
                    
                    where_condition = " OR ".join(where_parts)
                    
                    sql = f"""
                        SELECT {DB_ID_COL} as db_id,
                               {DB_FULLNAME_EXPR} as db_fullname,
                               {DB_EMAIL_COL} as db_email,
                               {DB_PHONE_COL} as db_phone,
                               {DB_BDAY_COL} as db_bday,
                               {DB_WAIVER_DATE_COL} as db_waiver_date
                        FROM {DB_TABLE}
                        WHERE {DB_BDAY_COL} = %s AND ({where_condition})
                    """
                    
                    print(f"[DEBUG] SQL: {sql}")
                    print(f"[DEBUG] Params: {params}")
                    
                    cur.execute(sql, params)
                    fetched = cur.fetchall()
                    if not fetched:
                        continue
                    
                    chunk_lower_map = {v.strip().lower(): v.strip() for v in chunk}
                    for row in fetched:
                        db_id = row.get("db_id")
                        db_full = (row.get("db_fullname") or "").strip()
                        db_email = (row.get("db_email") or "").strip().lower()
                        db_phone = (row.get("db_phone") or "").strip()
                        db_waiver_date = row.get("db_waiver_date")

                        # Format waiver date if it exists
                        formatted_waiver_date = ""
                        if db_waiver_date:
                            if hasattr(db_waiver_date, 'strftime'):
                                formatted_waiver_date = db_waiver_date.strftime("%Y-%m-%d")
                            else:
                                formatted_waiver_date = str(db_waiver_date)
                        
                        # Match by email first
                        if db_email and db_email in email_to_indices:
                            for ii in email_to_indices[db_email]:
                                if rows[ii].get("db_id", "") == "":
                                    rows[ii]["db_id"] = str(db_id)
                                    rows[ii]["waiver_date"] = formatted_waiver_date

                        # Match by phone
                        if db_phone and db_phone in phone_to_indices:
                            for ii in phone_to_indices[db_phone]:
                                if rows[ii].get("db_id", "") == "":
                                    rows[ii]["db_id"] = str(db_id)
                                    rows[ii]["waiver_date"] = formatted_waiver_date
                        
                        # Match by name if not matched by email/phone
                        if db_full:
                            db_full_lower = db_full.lower()
                            matching_variant = chunk_lower_map.get(db_full_lower)
                            if matching_variant is None:
                                for variant in chunk:
                                    if variant.strip().lower() == db_full_lower:
                                        matching_variant = variant.strip()
                                        break
                            if matching_variant:
                                input_idxs = idx_for_variant.get((matching_variant, str(dob)), []) or idx_for_variant.get((matching_variant, dob), [])
                                for ii in input_idxs:
                                    if rows[ii].get("db_id", "") == "":
                                        rows[ii]["db_id"] = str(db_id)
                                        rows[ii]["waiver_date"] = formatted_waiver_date
    finally:
        conn.close()

# ---------- CSV I/O ----------
def write_csv(path: str, rows: List[Dict[str, str]]):
    out_dir = os.path.dirname(path) or "."
    if out_dir and not os.path.exists(out_dir):
        os.makedirs(out_dir, exist_ok=True)
    with open(path, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=CSV_HEADER)
        writer.writeheader()
        for r in rows:
            out = {k: r.get(k, "") for k in CSV_HEADER}
            writer.writerow(out)

def read_csv(path: str) -> List[Dict[str, str]]:
    """Read CSV file and return list of dictionaries."""
    if not os.path.exists(path):
        raise FileNotFoundError(f"CSV file not found: {path!r}")
    
    rows = []
    with open(path, "r", newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Convert date format for database compatibility
            if 'dob' in row:
                row['dob'] = convert_date_format(row['dob'])
            rows.append(row)
    return rows

# ---------- CLI ----------
def main():
    p = argparse.ArgumentParser(description="Scan dir recursively and parse filenames from back, optional DB lookup.")
    p.add_argument("--input_dir", "-i", default="./waiver-data/2025", help="Root directory to scan.")
    # p.add_argument("--input_dir", "-i", default="./test", help="Root directory to scan.")
    p.add_argument("--output", "-o", default="./data/waiver-scanned.csv", help="Initial scan CSV.")
    p.add_argument("--final-output", "-f", default="./data/waiver-scanned-with-db.csv", help="Output after DB lookup.")
    p.add_argument("--scan-only", action="store_true", help="Only scan and write CSV.")
    p.add_argument("--host", default="localhost", help="DB host")
    p.add_argument("--port", type=int, default=3306, help="DB port")
    p.add_argument("--user", default="root", help="DB user")
    p.add_argument("--password", default="", help="DB password")
    p.add_argument("--database", default="", help="DB name (required unless --scan-only)")
    p.add_argument("--batch-size", type=int, default=BATCH_IN_SIZE, help="Batch size for IN(...) queries")
    args = p.parse_args()

    print(f"[INFO] Scanning: {args.input_dir!r}")
    rows = scan_directory(args.input_dir)
    print(f"[INFO] Parsed {len(rows)} files. Writing initial CSV: {args.output!r}")
    write_csv(args.output, rows)

    if args.scan_only:
        print("[INFO] scan-only -> done.")
        return

    # default connection (you can switch to using CLI args)
    conn_params = {
        "host": 'localhost',
        "port": 3306,
        "user": 'root',
        "password": "",
        "database": "climb_central",
    }

    print("[INFO] Looking up DB ids (this may take a while)...")
    # Transform rows to desired format
    rows =[]
    with open(args.output, "r") as f:
        reader = csv.DictReader(f)
        for parsed in reader:
            rows.append({
        "file_id": parsed.get("file_id", "") or "",
        "db_id": "",
        "first_name": parsed.get("first_name", "") or "",
        "middle_name": parsed.get("middle_name", "") or "",
        "last_name": parsed.get("last_name", "") or "",
        # "dob": parsed.get("dob", "") or "",
        "dob": convert_date_format(parsed.get("dob", "")),  # Convert date format for database compatibility
        "email": parsed.get("email", "") or "",
        "phone": parsed.get("phone", "") or "",
        "waiver_date": "",  # Will be populated from database
        "path": parsed.get("path", "") or "",
            })
    query_db_and_fill(rows, conn_params, batch_size=args.batch_size)
    print(f"[INFO] Writing final CSV: {args.final_output!r}")
    write_csv(args.final_output, rows)
    print("[DONE]")

if __name__ == "__main__":
    main()
